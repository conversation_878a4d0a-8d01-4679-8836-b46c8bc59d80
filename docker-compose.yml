services:
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "4318:4318" # OTLP HTTP receiver
      - "16686:16686" # Web UI
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    volumes:
      - jaeger_data:/badger
    networks:
      - jaeger

  loki:
    image: grafana/loki:3.4
    ports:
      - "3100:3100"
    networks:
      - loki

  grafana:
    environment:
      - GF_PATHS_PROVISIONING=/etc/grafana/provisioning
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
      - GF_FEATURE_TOGGLES_ENABLE=alertingSimplifiedRouting,alertingQueryAndExpressionsStepMode
    image: grafana/grafana:latest
    ports:
      - "3200:3000"
    networks:
      - loki
      - jaeger
    volumes:
      - grafana-storage:/var/lib/grafana

networks:
  loki:
    driver: bridge
  jaeger:
    driver: bridge

volumes:
  jaeger_data:
    driver: local
  grafana-storage:
    driver: local
