services:
  # Redis Cluster for Caching
  redis-master:
    image: redis:7-alpine
    container_name: eventista-redis-master
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_master_data:/data
    networks:
      - cache_network
    environment:
      - REDIS_REPLICATION_MODE=master

  redis-replica:
    image: redis:7-alpine
    container_name: eventista-redis-replica
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru --replicaof redis-master 6379
    depends_on:
      - redis-master
    volumes:
      - redis_replica_data:/data
    networks:
      - cache_network
    environment:
      - REDIS_REPLICATION_MODE=slave

  # Redis Commander for Management (Development only)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: eventista-redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis-master:6379
    depends_on:
      - redis-master
    networks:
      - cache_network
    profiles:
      - dev

  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "4318:4318" # OTLP HTTP receiver
      - "16686:16686" # Web UI
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    volumes:
      - jaeger_data:/badger
    networks:
      - jaeger

  loki:
    image: grafana/loki:3.4
    ports:
      - "3100:3100"
    networks:
      - loki

  grafana:
    environment:
      - GF_PATHS_PROVISIONING=/etc/grafana/provisioning
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Admin
      - GF_FEATURE_TOGGLES_ENABLE=alertingSimplifiedRouting,alertingQueryAndExpressionsStepMode
    image: grafana/grafana:latest
    ports:
      - "3200:3000"
    networks:
      - loki
      - jaeger
    volumes:
      - grafana-storage:/var/lib/grafana

networks:
  cache_network:
    driver: bridge
  loki:
    driver: bridge
  jaeger:
    driver: bridge

volumes:
  redis_master_data:
    driver: local
  redis_replica_data:
    driver: local
  jaeger_data:
    driver: local
  grafana-storage:
    driver: local
