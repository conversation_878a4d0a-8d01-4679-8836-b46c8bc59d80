const nodemailer = require("nodemailer");
const Handlebars = require("handlebars");
const fs = require("fs").promises;
const { SES, SendEmailCommand } = require("@aws-sdk/client-ses");
const mjml2html = require("mjml");

function formatMoney(amount, currencyCode, locale) {
  console.log(1111, amount, currencyCode, locale);

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currencyCode,
  }).format(amount);
}

Handlebars.registerHelper(
  "customFormatMoney",
  function (amount, currencyCode, locale) {
    console.log(2222, amount, currencyCode, locale);
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  }
);
Handlebars.registerHelper("log", function (value) {
  console.log("From Handlebars");
  if (value) {
    console.log(JSON.stringify(value));
  } else {
    // @ts-ignore
    console.log(JSON.stringify(this));
  }
  console.log("From Handlebars end");
  return "";
});
const order = {
  createdAt: "2025-01-16T06:46:03.773Z",
  updatedAt: "2025-01-17T02:51:32.496Z",
  type: "Regular",
  code: "4FN1XQKF8VHP4UAW",
  state: "PaymentSettled",
  active: false,
  orderPlacedAt: null,
  couponCodes: ["TEST"],
  shippingAddress: {
    fullName: "erger",
    streetLine1: "sxfg",
    province: "Tỉnh Thái Nguyên",
    countryCode: "VN",
    phoneNumber: "rth",
    customFields: {
      district: "Huyện Đồng Hỷ",
      ward: "Xã Khe Mo",
      country: "Vietnam",
    },
  },
  billingAddress: null,
  currencyCode: "VND",
  id: 149,
  aggregateOrderId: null,
  customerId: 30,
  taxZoneId: 1,
  subTotal: 54000,
  subTotalWithTax: 54000,
  shipping: 0,
  shippingWithTax: 0,
  lines: [
    {
      createdAt: "2025-01-16T06:46:03.773Z",
      updatedAt: "2025-01-17T02:51:08.817Z",
      quantity: 3,
      orderPlacedQuantity: 0,
      listPriceIncludesTax: false,
      adjustments: [
        {
          amount: -6000,
          adjustmentSource: "PROMOTION:1",
          description: "test",
          type: "DISTRIBUTED_ORDER_PROMOTION",
          data: {
            itemDistribution: [-2000, -2000, -2000],
          },
        },
      ],
      productVariant: {
        createdAt: "2024-12-31T04:14:21.312Z",
        updatedAt: "2025-01-16T04:36:59.336Z",
        deletedAt: null,
        enabled: true,
        sku: null,
        outOfStockThreshold: 0,
        useGlobalOutOfStockThreshold: true,
        trackInventory: false,
        id: 14,
        featuredAssetId: 6,
        taxCategoryId: 2,
        productId: 3,
        customFields: {
          compareAtPrice: null,
          limited: null,
        },
        featuredAsset: {
          createdAt: "2025-01-16T04:36:44.938Z",
          updatedAt: "2025-01-16T04:36:44.938Z",
          name: "employee-2.png",
          type: "IMAGE",
          mimeType: "image/png",
          width: 690,
          height: 830,
          fileSize: 85354,
          source: "source/b6/employee-2.png",
          preview: "preview/50/employee-2__preview.png",
          focalPoint: null,
          id: 6,
        },
        translations: [
          {
            createdAt: "2024-12-31T04:14:21.312Z",
            updatedAt: "2024-12-31T04:14:21.312Z",
            languageCode: "en",
            name: "product1-test S Red",
            id: 14,
          },
        ],
      },
      price: 0,
      priceWithTax: 0,
      unitPrice: 20000,
      unitPriceWithTax: 20000,
      discountedUnitPrice: 20000,
      discountedUnitPriceWithTax: 20000,
      proratedUnitPrice: 18000,
      proratedUnitPriceWithTax: 18000,
      linePrice: 60000,
      linePriceWithTax: 60000,
      discountedLinePrice: 60000,
      discountedLinePriceWithTax: 60000,
      proratedLinePrice: 54000,
      proratedLinePriceWithTax: 54000,
    },
  ],
  customer: {
    createdAt: "2025-01-08T06:59:49.941Z",
    updatedAt: "2025-01-08T06:59:49.941Z",
    deletedAt: null,
    title: null,
    firstName: "lang",
    lastName: "bach",
    phoneNumber: null,
    emailAddress: "<EMAIL>",
    id: 30,
    user: {
      createdAt: "2025-01-08T06:59:49.941Z",
      updatedAt: "2025-01-17T02:37:01.720Z",
      deletedAt: null,
      identifier: "<EMAIL>",
      verified: true,
      lastLogin: "2025-01-17T02:37:03.694Z",
      id: 12,
    },
  },
  discounts: [
    // {
    //   amount: -6000,
    //   adjustmentSource: "PROMOTION:1",
    //   description: "test",
    //   type: "DISTRIBUTED_ORDER_PROMOTION",
    //   data: {
    //     itemDistribution: [-2000, -2000, -2000],
    //   },
    //   amountWithTax: -6000,
    // },
  ],
  total: 54000,
  totalWithTax: 54000,
  totalQuantity: 3,
  taxSummary: [
    {
      taxRate: 0,
      description: "No configured tax rate",
      taxBase: 54000,
      taxTotal: 0,
    },
  ],
};

async function loadTemplate(templatePath, data) {
  const template = await fs.readFile(templatePath, "utf-8");
  const compiledTemplate = Handlebars.compile(template);
  const { html } = mjml2html(compiledTemplate(data));
  return html;
}

const transporter = nodemailer.createTransport({
  host: "pro12.emailserver.vn",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "Eventista@2024",
  },
  debug: true,
  logger: true,
});

async function testEmail() {
  try {
    const totalDiscount = order.discounts
      .map((v) => v.amount)
      .reduce((a, b) => a + b, 0);
    console.log(98999, totalDiscount);

    const htmlContent = await loadTemplate(
      "./static/email/templates/order-confirmation/body.hbs",
      {
        name: "John Doe",
        message: "Welcome to our service!",
        order,
        customFormatMoney: formatMoney,
        assetsHost: "http://*************:5678/assets/",
        totalDiscount,
        finalPrice: order.total + totalDiscount,
      }
    );

    // const info = await transporter.sendMail({
    //   from: "<EMAIL>",
    //   to: "<EMAIL>",
    //   subject: "Test Email",
    //   html: htmlContent,
    // });
    // console.log("Message sent:", info);
  } catch (error) {
    console.error("Error:", error);
  }
}

async function testSES() {
  try {
    const htmlContent = await loadTemplate(
      "./static/email/templates/order-confirmation/body.hbs",
      {
        name: "John Doe",
        message: "Welcome to our service!",
      }
    );
    const ses = new SES({
      apiVersion: "2010-12-01",
      region: "ap-southeast-1",
      credentials: {
        accessKeyId: "********************",
        secretAccessKey: "pYndf9zgcwRhVjd2qJ+8HiKK8EL8nOPe7ItwEcID",
      },
    });
    const command = new SendEmailCommand({
      Destination: {
        /* required */
        CcAddresses: [
          /* more items */
        ],
        ToAddresses: ["<EMAIL>"],
      },
      Message: {
        /* required */
        Body: {
          /* required */
          Html: {
            Charset: "UTF-8",
            Data: htmlContent,
          },
        },
        Subject: {
          Charset: "UTF-8",
          Data: "Test Email SES",
        },
      },
      Source: "<EMAIL>",
      ReplyToAddresses: [
        /* more items */
      ],
    });
    const result = await ses.send(command);
    console.log("Message sent:", result);
  } catch (error) {
    console.error("Error:", error);
  }
}
// testSES();
testEmail();
