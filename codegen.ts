import type { CodegenConfig } from "@graphql-codegen/cli";

const config: CodegenConfig = {
  // This assumes your server is running on the standard port
  // and with the default admin API path. Adjust accordingly.
  schema: "http://localhost:5678/v1/vendure/shop-api",
  generates: {
    // The path to the generated type file in your
    // plugin directory. Adjust accordingly.
    "src/gql/generated.ts": {
      plugins: ["typescript"],
    },
  },
};

export default config;
