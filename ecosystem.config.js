module.exports = {
  apps: [
    {
      name: "vendure-main",
      script: "./dist/index.js",
      // node_args: "-r newrelic",
      // node_args: "--require ./dist/preload.js",
      env: {
        NODE_ENV: "production",
        // NEW_RELIC_APP_NAME: "merchandise-prod",
        // NEW_RELIC_LICENSE_KEY: "2c477b014a68f4ca90e7f4d576941d04FFFFNRAL",
        // OTEL_RESOURCE_ATTRIBUTES: "service.name=vendure-main",
      },
      env_development: {
        NODE_ENV: "development",
        // NEW_RELIC_APP_NAME: "merchandise-dev",
        // NEW_RELIC_LICENSE_KEY: "c98e1e4d3e46c0fed17667ea8f564075FFFFNRAL",
        // OTEL_RESOURCE_ATTRIBUTES: "service.name=vendure-main",
      },
    },
    {
      name: "vendure-worker",
      script: "./dist/index-worker.js",
      // node_args: "-r newrelic",
      // node_args: "--require ./dist/preload.js",
      env: {
        NODE_ENV: "production",
        // NEW_RELIC_APP_NAME: "merchandise-prod",
        // NEW_RELIC_LICENSE_KEY: "2c477b014a68f4ca90e7f4d576941d04FFFFNRAL",
        // OTEL_RESOURCE_ATTRIBUTES: "service.name=vendure-worker",
      },
      env_development: {
        NODE_ENV: "development",
        // NEW_RELIC_APP_NAME: "merchandise-dev",
        // NEW_RELIC_LICENSE_KEY: "c98e1e4d3e46c0fed17667ea8f564075FFFFNRAL",
        // OTEL_RESOURCE_ATTRIBUTES: "service.name=vendure-worker",
      },
    },
  ],
};
