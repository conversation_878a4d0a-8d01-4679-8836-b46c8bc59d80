{"compilerOptions": {"module": "NodeNext", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "moduleResolution": "nodenext", "target": "es2019", "strict": true, "sourceMap": false, "skipLibCheck": true, "outDir": "./dist", "baseUrl": "./", "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*", "src/types/**/*"], "exclude": ["node_modules", "migration.ts", "src/plugins/**/ui/*", "admin-ui", "codegen.ts"], "ts-node": {"files": true}}