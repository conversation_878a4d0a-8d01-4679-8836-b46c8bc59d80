import {MigrationInterface, QueryRunner} from "typeorm";

export class AddCustomFields1751870424294 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "fulfillment" ADD "customFieldsExtradata" text`, undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "fulfillment" DROP COLUMN "customFieldsExtradata"`, undefined);
   }

}
