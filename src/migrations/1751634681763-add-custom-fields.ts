import {MigrationInterface, QueryRunner} from "typeorm";

export class AddCustomFields1751634681763 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "shipping_method" DROP COLUMN "customFieldsToken"`, undefined);
        await queryRunner.query(`ALTER TABLE "shipping_method" ADD "customFieldsToken" text`, undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "shipping_method" DROP COLUMN "customFieldsToken"`, undefined);
        await queryRunner.query(`ALTER TABLE "shipping_method" ADD "customFieldsToken" character varying(255)`, undefined);
   }

}
