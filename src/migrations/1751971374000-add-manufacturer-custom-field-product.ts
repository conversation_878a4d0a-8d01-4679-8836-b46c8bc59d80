import {MigrationInterface, QueryRunner} from "typeorm";

export class AddManufacturerCustomFieldProduct1751971374000 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "product" ADD "customFieldsManufacturer" character varying(255)`, undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "product" DROP COLUMN "customFieldsManufacturer"`, undefined);
   }

} 