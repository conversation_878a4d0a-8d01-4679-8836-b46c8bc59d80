import {MigrationInterface, QueryRunner} from "typeorm";

export class AddCustomFieldCustomer1751438885638 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "customer" ADD "customFieldsExternaluserid" character varying(255)`, undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "customer" DROP COLUMN "customFieldsExternaluserid"`, undefined);
   }

}
