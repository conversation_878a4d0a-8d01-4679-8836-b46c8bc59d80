import { MigrationInterface, QueryRunner } from "typeorm";

export class AddVariantPriceIndex1752312237582 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(
      `CREATE INDEX "IDX_product_variant_price_price" ON "product_variant_price" ("price") `,
      undefined
    );
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_product_variant_price_price"`,
      undefined
    );
  }
}
