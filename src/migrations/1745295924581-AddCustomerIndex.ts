import { MigrationInterface, QueryRunner } from "typeorm";

export class AddEntityIndex1745295924581 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the index on the customer table's phoneNumber field
    await queryRunner.query(
      `CREATE INDEX "IDX_customer_phone_number" ON "customer" ("phoneNumber")`,
      undefined
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_customer_email_address" ON "customer" ("emailAddress")`,
      undefined
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove the index if the migration is reverted
    await queryRunner.query(
      `DROP INDEX "IDX_customer_phone_number"`,
      undefined
    );
    await queryRunner.query(
      `DROP INDEX "IDX_customer_email_address"`,
      undefined
    );
  }
}
