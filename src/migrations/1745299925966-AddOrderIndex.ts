import { MigrationInterface, QueryRunner } from "typeorm";

export class AddOrderIndex1745299925966 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(
      `CREATE INDEX "IDX_order_createdAt" ON "order" ("createdAt") `,
      undefined
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_order_state" ON "order" ("state") `,
      undefined
    );
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.query(
      `DROP INDEX "public"."IDX_order_createdAt"`,
      undefined
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_order_state"`, undefined);
  }
}
