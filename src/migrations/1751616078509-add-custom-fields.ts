import {MigrationInterface, QueryRunner} from "typeorm";

export class AddCustomFields1751616078509 implements MigrationInterface {

   public async up(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "shipping_method" ADD "customFieldsToken" character varying(255)`, undefined);
   }

   public async down(queryRunner: QueryRunner): Promise<any> {
        await queryRunner.query(`ALTER TABLE "shipping_method" DROP COLUMN "customFieldsToken"`, undefined);
   }

}
