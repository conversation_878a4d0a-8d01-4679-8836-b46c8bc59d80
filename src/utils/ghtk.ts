import { Order, StockLocation } from '@vendure/core'
import {request} from 'https'

export type CreateOrderGhtkOrder = {
    partner_id: string
    label: string
    area: string
    fee: string
    insurance_fee: string
    estimated_pick_time: string
    estimated_deliver_time: string
    status_id: string
    tracking_id: number
}
export type ShippingFeeGhtkOrder = {
    name: string
    fee: number
    insurance_fee: number
    include_vat: string
    cost_id: string
    delivery_type: string
    dt: string
    extFees: Array<unknown>
    ship_fee_only: number
    promotion_key: string
    delivery: boolean
}
export type CreateOrderGhtkResponse = {
    success: boolean
    message: string
    order: CreateOrderGhtkOrder
}
export type ShippingFeeGhtkResponse = {
    success: boolean
    message: string
    fee: ShippingFeeGhtkOrder
}

export const createOrder = async (order: Order, stock: StockLocation): Promise<CreateOrderGhtkOrder> => {
    return new Promise((resolve, reject) => {
        const products = []
        const stockInfo = {
            // @ts-ignore
            address: stock.customFields.address,
            // @ts-ignore
            province: stock.customFields.province,
            // @ts-ignore
            district: stock.customFields.district,
            // @ts-ignore
            ward: stock.customFields.ward,
            name: stock.name,
            // @ts-ignore
            phone: stock.customFields.phone
        }
        for (const line of order.lines) {
            products.push({name: line.productVariant.sku, weight: 0.1, quantity: line.quantity, product_code: line.productVariant.id})
        }
        const postData = JSON.stringify({
            "products": products,
            "order": {
                "id": order.code,
                "pick_name": stockInfo.name,
                "pick_address": stockInfo.address,
                "pick_province": stockInfo.province,
                "pick_district": stockInfo.district,
                "pick_ward": stockInfo.ward,
                "pick_tel": stockInfo.phone,
                "pick_money": order.subTotalWithTax / 100,
                "tel": order.shippingAddress.phoneNumber,
                "name": order.shippingAddress.fullName,
                "address": order.shippingAddress.streetLine1,
                "province": order.shippingAddress.province,
                "district": order.shippingAddress.customFields.district,
                "ward": order.shippingAddress.customFields.ward,
                "hamlet": "Khác",
                "is_freeship": "0",
                "value": order.subTotalWithTax / 100,
                "transport": "road",
                "pick_option": "cod"
            }
        })
        
        const req = request({
            hostname: 'services-staging.ghtklab.com',
            path: '/services/shipment/order/?ver=1.5',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData),
                Token: 'C407bE86320a2766AB53054Dc19ee8c78E738b9b',
            },
        }, (res) => {
            res.on('data', (body) => {
                const data = JSON.parse(body) as CreateOrderGhtkResponse;
                console.log(11111, data);
                
                if (data.success) {
                    resolve(data.order)
                }
                
            })
        })
        req.on('error', (err) => {
            console.log(2222, err);
            
            reject(err)
        })
        req.write(postData)
        req.end()
    })
}

export const calculateShippingFee = async (order: Order, stock: StockLocation): Promise<ShippingFeeGhtkOrder> => {
    return new Promise((resolve, reject) => {
        const url = new URL('https://services-staging.ghtklab.com/services/shipment/fee');
        url.searchParams.append('address', order.shippingAddress.streetLine1 ?? '');
        url.searchParams.append('province', order.shippingAddress.province ?? '');
        url.searchParams.append('district', order.shippingAddress.customFields.district ?? '')
        // @ts-ignore;
        url.searchParams.append('pick_province', stock.customFields.province);
        // @ts-ignore
        url.searchParams.append('pick_district', stock.customFields.district);
        url.searchParams.append('weight', '0.1');

        const req = request({
            hostname: 'services-staging.ghtklab.com',
            path: url.pathname + url.search,
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                Token: 'C407bE86320a2766AB53054Dc19ee8c78E738b9b',
            },
        }, (res) => {
            res.on('data', (body) => {
                const data = JSON.parse(body) as ShippingFeeGhtkResponse;
                console.log('calculateShipping res: ', data);
                
                if (data.success) {
                    resolve(data.fee)
                }
                
            })
        })
        req.on('error', (err) => {
            console.log(2222, err);
            
            reject(err)
        })
        req.end()
    })
}