import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { GqlExecutionContext } from '@nestjs/graphql';
import { CoreCacheService } from '../services/core-cache.service';
import { CachedOrderService } from '../services/cached-order.service';
import { CachedProductService } from '../services/cached-product.service';
import { CachedCustomerService } from '../services/cached-customer.service';

/**
 * Cache Interceptor - Intercepts GraphQL and REST API calls to provide caching
 * This interceptor works for ALL Vendure APIs (Shop, Admin, REST)
 */
@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private cacheService: CoreCacheService,
    private cachedOrderService: CachedOrderService,
    private cachedProductService: CachedProductService,
    private cachedCustomerService: CachedCustomerService
  ) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const contextType = context.getType<'http' | 'graphql'>();
    const isGraphQL = contextType === 'graphql';
    const isRest = contextType === 'http';

    if (isGraphQL) {
      return this.handleGraphQLRequest(context, next);
    } else if (isRest) {
      return this.handleRestRequest(context, next);
    }

    return next.handle();
  }

  private handleGraphQLRequest(context: ExecutionContext, next: CallHandler): Observable<any> {
    const gqlContext = GqlExecutionContext.create(context);
    const info = gqlContext.getInfo();
    const args = gqlContext.getArgs();
    const ctx = gqlContext.getContext().req?.vendureContext;

    // Only cache read operations (queries)
    if (info.operation.operation !== 'query') {
      return next.handle().pipe(
        tap(() => {
          // Invalidate cache for mutations
          this.handleMutationInvalidation(info.fieldName, args);
        })
      );
    }

    // Generate cache key for query
    const cacheKey = this.generateGraphQLCacheKey(info.fieldName, args, ctx);
    const ttl = this.getTTLForOperation(info.fieldName);

    return new Observable(observer => {
      // Try to get from cache first
      this.cacheService.get(cacheKey).then(cachedResult => {
        if (cachedResult !== null) {
          observer.next(cachedResult);
          observer.complete();
          return;
        }

        // Execute the actual resolver
        next.handle().subscribe({
          next: (result) => {
            // Cache the result
            this.cacheService.set(cacheKey, result, { ttl });
            observer.next(result);
          },
          error: (error) => observer.error(error),
          complete: () => observer.complete()
        });
      }).catch(error => {
        // If cache fails, just execute the resolver
        next.handle().subscribe(observer);
      });
    });
  }

  private handleRestRequest(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const method = request.method;
    const url = request.url;

    // Only cache GET requests
    if (method !== 'GET') {
      return next.handle().pipe(
        tap(() => {
          // Invalidate cache for non-GET requests
          this.handleRestInvalidation(method, url);
        })
      );
    }

    const cacheKey = this.generateRestCacheKey(url, request.query);
    const ttl = this.getTTLForRestEndpoint(url);

    return new Observable(observer => {
      this.cacheService.get(cacheKey).then(cachedResult => {
        if (cachedResult !== null) {
          observer.next(cachedResult);
          observer.complete();
          return;
        }

        next.handle().subscribe({
          next: (result) => {
            this.cacheService.set(cacheKey, result, { ttl });
            observer.next(result);
          },
          error: (error) => observer.error(error),
          complete: () => observer.complete()
        });
      }).catch(error => {
        next.handle().subscribe(observer);
      });
    });
  }

  private generateGraphQLCacheKey(fieldName: string, args: any, ctx: any): string {
    const contextInfo = {
      channelId: ctx?.channel?.id,
      languageCode: ctx?.languageCode,
      userId: ctx?.activeUserId
    };

    const keyData = {
      type: 'graphql',
      field: fieldName,
      args,
      context: contextInfo
    };

    const hash = Buffer.from(JSON.stringify(keyData)).toString('base64').slice(0, 16);
    return `gql:${fieldName}:${hash}`;
  }

  private generateRestCacheKey(url: string, query: any): string {
    const keyData = {
      type: 'rest',
      url,
      query
    };

    const hash = Buffer.from(JSON.stringify(keyData)).toString('base64').slice(0, 16);
    return `rest:${hash}`;
  }

  private getTTLForOperation(fieldName: string): number {
    // Define TTL based on operation type
    const ttlMap: { [key: string]: number } = {
      // Order operations
      'orders': 300,        // 5 minutes
      'order': 600,         // 10 minutes
      'orderByCode': 300,   // 5 minutes

      // Product operations
      'products': 900,      // 15 minutes
      'product': 1800,      // 30 minutes
      'productVariants': 900, // 15 minutes

      // Customer operations
      'customers': 600,     // 10 minutes
      'customer': 1200,     // 20 minutes

      // Configuration (longer cache)
      'channels': 3600,     // 1 hour
      'paymentMethods': 1800, // 30 minutes
      'shippingMethods': 1800, // 30 minutes
    };

    return ttlMap[fieldName] || 300; // Default 5 minutes
  }

  private getTTLForRestEndpoint(url: string): number {
    if (url.includes('/orders')) return 300;      // 5 minutes
    if (url.includes('/products')) return 900;    // 15 minutes
    if (url.includes('/customers')) return 600;   // 10 minutes
    if (url.includes('/channels')) return 3600;   // 1 hour

    return 300; // Default 5 minutes
  }

  private async handleMutationInvalidation(fieldName: string, args: any): Promise<void> {
    try {
      if (fieldName.includes('order') || fieldName.includes('Order')) {
        const orderId = args.id || args.orderId;
        if (orderId) {
          await this.cachedOrderService.invalidateOrderCache(orderId, args);
        }
      }

      if (fieldName.includes('product') || fieldName.includes('Product')) {
        const productId = args.id || args.productId;
        if (productId) {
          await this.cachedProductService.invalidateProductCache(productId, args);
        }
      }

      if (fieldName.includes('customer') || fieldName.includes('Customer')) {
        const customerId = args.id || args.customerId;
        if (customerId) {
          await this.cachedCustomerService.invalidateCustomerCache(customerId);
        }
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  private async handleRestInvalidation(method: string, url: string): Promise<void> {
    try {
      // Extract entity type and ID from URL
      const urlParts = url.split('/');
      const entityType = urlParts[urlParts.length - 2];
      const entityId = urlParts[urlParts.length - 1];

      if (entityType === 'orders' && entityId) {
        await this.cachedOrderService.invalidateOrderCache(entityId);
      } else if (entityType === 'products' && entityId) {
        await this.cachedProductService.invalidateProductCache(entityId);
      } else if (entityType === 'customers' && entityId) {
        await this.cachedCustomerService.invalidateCustomerCache(entityId);
      }
    } catch (error) {
      console.error('REST cache invalidation error:', error);
    }
  }
}