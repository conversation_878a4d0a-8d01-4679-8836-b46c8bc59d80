import { Controller, Get, Post, Delete, Query, Body } from '@nestjs/common';
import { CoreCacheService } from '../services/core-cache.service';
import { CacheMonitorService } from '../services/cache-monitor.service';
import { CacheInvalidationService } from '../services/cache-invalidation.service';

@Controller('api/v1/cache')
export class CacheDashboardController {
  constructor(
    private cacheService: CoreCacheService,
    private cacheMonitorService: CacheMonitorService,
    private cacheInvalidationService: CacheInvalidationService
  ) {}

  /**
   * Get cache metrics and statistics
   */
  @Get('metrics')
  async getMetrics() {
    return this.cacheService.getStats();
  }

  /**
   * Get cache performance report
   */
  @Get('report')
  async getReport() {
    return this.cacheMonitorService.generateReport();
  }

  /**
   * Get cache statistics
   */
  @Get('stats')
  async getStats() {
    return this.cacheService.getStats();
  }

  /**
   * Clear all cache
   */
  @Delete('clear')
  async clearCache() {
    await this.cacheService.clear();
    return { success: true, message: 'All cache cleared' };
  }

  /**
   * Clear cache by pattern
   */
  @Delete('pattern')
  async clearCachePattern(@Query('pattern') pattern: string) {
    if (!pattern) {
      return { success: false, message: 'Pattern is required' };
    }

    const deletedCount = await this.cacheService.delPattern(pattern);
    return {
      success: true,
      message: `Cleared ${deletedCount} cache entries matching pattern: ${pattern}`
    };
  }

  /**
   * Invalidate cache for specific entity
   */
  @Post('invalidate')
  async invalidateEntity(@Body() body: {
    entityType: string;
    entityId: number;
    additionalData?: any;
  }) {
    await this.cacheInvalidationService.invalidateEntity(
      body.entityType,
      body.entityId,
      body.additionalData
    );

    return {
      success: true,
      message: `Cache invalidated for ${body.entityType} ${body.entityId}`
    };
  }

  /**
   * Bulk invalidate cache for multiple entities
   */
  @Post('invalidate/bulk')
  async bulkInvalidate(@Body() body: {
    invalidations: Array<{
      entityType: string;
      entityId: number;
      additionalData?: any;
    }>;
  }) {
    await this.cacheInvalidationService.bulkInvalidate(body.invalidations);

    return {
      success: true,
      message: `Bulk cache invalidation completed for ${body.invalidations.length} entities`
    };
  }

  /**
   * Get cache key information
   */
  @Get('key')
  async getCacheKey(@Query('key') key: string) {
    if (!key) {
      return { success: false, message: 'Key is required' };
    }

    const value = await this.cacheService.get(key);
    return {
      success: true,
      key,
      exists: value !== null,
      value: value
    };
  }

  /**
   * Set cache key manually (for testing)
   */
  @Post('key')
  async setCacheKey(@Body() body: {
    key: string;
    value: any;
    ttl?: number;
  }) {
    const success = await this.cacheService.set(body.key, body.value, { ttl: body.ttl });
    return {
      success,
      message: success ? 'Cache key set successfully' : 'Failed to set cache key'
    };
  }

  /**
   * Delete specific cache key
   */
  @Delete('key')
  async deleteCacheKey(@Query('key') key: string) {
    if (!key) {
      return { success: false, message: 'Key is required' };
    }

    const success = await this.cacheService.del(key);
    return {
      success,
      message: success ? 'Cache key deleted successfully' : 'Failed to delete cache key'
    };
  }

  /**
   * Health check for cache system
   */
  @Get('health')
  async healthCheck() {
    return this.cacheMonitorService.getHealthStatus();
  }
}