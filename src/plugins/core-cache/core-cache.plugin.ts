import { PluginCommonModule, VendurePlugin, Type } from '@vendure/core';
import { CoreCacheService } from './services/core-cache.service';
import { CachedOrderService } from './services/cached-order.service';
import { CachedProductService } from './services/cached-product.service';
import { CachedCustomerService } from './services/cached-customer.service';
import { CacheInvalidationService } from './services/cache-invalidation.service';
import { CacheMonitorService } from './services/cache-monitor.service';
import { CacheInterceptor } from './interceptors/cache.interceptor';
import { APP_INTERCEPTOR } from '@nestjs/core';

/**
 * Core Cache Plugin - Provides caching for all Vendure APIs
 * This plugin integrates with Vendure's core services to provide
 * caching for GraphQL (Shop & Admin) and REST APIs
 */
@VendurePlugin({
  imports: [PluginCommonModule],
  providers: [
    CoreCacheService,
    CachedOrderService,
    CachedProductService,
    CachedCustomerService,
    CacheInvalidationService,
    CacheMonitorService,
    {
      provide: APP_INTERCEPTOR,
      useClass: CacheInterceptor,
    },
  ],
  exports: [
    CoreCacheService,
    CachedOrderService,
    CachedProductService,
    CachedCustomerService,
    CacheInvalidationService,
    CacheMonitorService,
  ],
  configuration: config => {
    // Override core services with cached versions
    config.serviceOverrides = [
      ...(config.serviceOverrides || []),
      {
        service: 'OrderService',
        useClass: CachedOrderService as Type<any>,
      },
      {
        service: 'ProductService',
        useClass: CachedProductService as Type<any>,
      },
      {
        service: 'CustomerService',
        useClass: CachedCustomerService as Type<any>,
      },
    ];

    return config;
  },
})
export class CoreCachePlugin {
  static options: {
    redis?: {
      host?: string;
      port?: number;
      password?: string;
      cluster?: boolean;
    };
    defaultTTL?: number;
    enableMetrics?: boolean;
  } = {};

  static init(options?: typeof CoreCachePlugin.options) {
    CoreCachePlugin.options = { ...CoreCachePlugin.options, ...options };
    return CoreCachePlugin;
  }
}