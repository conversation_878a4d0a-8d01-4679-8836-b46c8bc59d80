import { Injectable } from '@nestjs/common';
import { CoreCacheService, CacheStats } from './core-cache.service';

/**
 * Cache Monitor Service - Provides monitoring and analytics for cache performance
 */
@Injectable()
export class CacheMonitorService {
  constructor(private cacheService: CoreCacheService) {}

  /**
   * Get current cache statistics
   */
  getStats(): CacheStats {
    return this.cacheService.getStats();
  }

  /**
   * Get cache health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded';
    redis: { connected: boolean; fallbackMode: boolean };
    stats: CacheStats;
    timestamp: Date;
  } {
    const stats = this.getStats();
    const isRedisAvailable = this.cacheService.isRedisAvailable();

    return {
      status: isRedisAvailable ? 'healthy' : 'degraded',
      redis: {
        connected: isRedisAvailable,
        fallbackMode: !isRedisAvailable
      },
      stats,
      timestamp: new Date()
    };
  }

  /**
   * Generate performance report
   */
  generateReport(): {
    summary: CacheStats;
    recommendations: string[];
    timestamp: Date;
  } {
    const stats = this.getStats();
    const recommendations: string[] = [];

    if (stats.hitRate < 70) {
      recommendations.push('Consider increasing cache TTL for frequently accessed data');
      recommendations.push('Review cache key patterns to ensure optimal caching strategy');
    }

    if (!this.cacheService.isRedisAvailable()) {
      recommendations.push('Restore Redis connection for better performance and persistence');
    }

    if (recommendations.length === 0) {
      recommendations.push('Cache performance is optimal');
    }

    return {
      summary: stats,
      recommendations,
      timestamp: new Date()
    };
  }
}