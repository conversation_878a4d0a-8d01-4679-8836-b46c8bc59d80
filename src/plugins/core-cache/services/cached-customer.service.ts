import { Injectable } from '@nestjs/common';
import { CoreCacheService } from './core-cache.service';

/**
 * Cached Customer Service - Provides caching utilities for customer operations
 */
@Injectable()
export class CachedCustomerService {
  constructor(private cacheService: CoreCacheService) {}

  /**
   * Generate cache key for customer operations
   */
  generateCacheKey(operation: string, params: any): string {
    const paramHash = Buffer.from(JSON.stringify(params)).toString('base64').slice(0, 16);
    return `customer:${operation}:${paramHash}`;
  }

  /**
   * Invalidate customer-related cache
   */
  async invalidateCustomerCache(customerId: string | number): Promise<void> {
    const patterns = [
      `customer:*:*${customerId}*`,
      `customers:*`,
      `session:*${customerId}*`,
      `auth:*${customerId}*`
    ];

    // Invalidate all matching patterns
    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }
}