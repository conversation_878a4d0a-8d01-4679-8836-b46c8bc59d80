import { Injectable } from '@nestjs/common';
import { CoreCacheService } from './core-cache.service';

/**
 * Cached Order Service - Provides caching utilities for order operations
 * This service is used by the cache interceptor for all APIs
 */
@Injectable()
export class CachedOrderService {
  constructor(private cacheService: CoreCacheService) {}

  /**
   * Generate cache key for order operations
   */
  generateCacheKey(operation: string, params: any): string {
    const paramHash = Buffer.from(JSON.stringify(params)).toString('base64').slice(0, 16);
    return `order:${operation}:${paramHash}`;
  }

  /**
   * Invalidate order-related cache
   */
  async invalidateOrderCache(orderId: string | number, additionalData?: any): Promise<void> {
    const patterns = [
      `order:*:*${orderId}*`,
      `orders:*`,
      'customer:*:order-history:*'
    ];

    if (additionalData?.customerId) {
      patterns.push(`customer:${additionalData.customerId}:*`);
    }

    if (additionalData?.orderCode) {
      patterns.push(`order:*:*${additionalData.orderCode}*`);
    }

    // Invalidate all matching patterns
    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }
}