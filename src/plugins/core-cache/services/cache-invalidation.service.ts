import { Injectable } from '@nestjs/common';
import { CoreCacheService } from './core-cache.service';
import { CachedOrderService } from './cached-order.service';
import { CachedProductService } from './cached-product.service';
import { CachedCustomerService } from './cached-customer.service';

/**
 * Cache Invalidation Service - Handles cache invalidation for all entities
 */
@Injectable()
export class CacheInvalidationService {
  constructor(
    private cacheService: CoreCacheService,
    private cachedOrderService: CachedOrderService,
    private cachedProductService: CachedProductService,
    private cachedCustomerService: CachedCustomerService
  ) {}

  /**
   * Invalidate cache by entity type and ID
   */
  async invalidateEntity(entityType: string, entityId: string | number, additionalData?: any): Promise<void> {
    switch (entityType.toLowerCase()) {
      case 'order':
        await this.cachedOrderService.invalidateOrderCache(entityId, additionalData);
        break;
      case 'product':
        await this.cachedProductService.invalidateProductCache(entityId, additionalData);
        break;
      case 'customer':
        await this.cachedCustomerService.invalidateCustomerCache(entityId);
        break;
      default:
        console.warn(`Unknown entity type for cache invalidation: ${entityType}`);
    }
  }

  /**
   * Bulk invalidation for multiple entities
   */
  async bulkInvalidate(invalidations: Array<{
    entityType: string;
    entityId: string | number;
    additionalData?: any;
  }>): Promise<void> {
    const promises = invalidations.map(({ entityType, entityId, additionalData }) =>
      this.invalidateEntity(entityType, entityId, additionalData)
    );

    await Promise.all(promises);
  }

  /**
   * Clear all cache
   */
  async clearAll(): Promise<void> {
    await this.cacheService.clear();
  }

  /**
   * Clear cache by pattern
   */
  async clearPattern(pattern: string): Promise<number> {
    return this.cacheService.delPattern(pattern);
  }
}