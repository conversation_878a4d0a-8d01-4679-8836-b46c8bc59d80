import { Injectable } from '@nestjs/common';
import { CoreCacheService } from './core-cache.service';

/**
 * Cached Product Service - Provides caching utilities for product operations
 */
@Injectable()
export class CachedProductService {
  constructor(private cacheService: CoreCacheService) {}

  /**
   * Generate cache key for product operations
   */
  generateCacheKey(operation: string, params: any): string {
    const paramHash = Buffer.from(JSON.stringify(params)).toString('base64').slice(0, 16);
    return `product:${operation}:${paramHash}`;
  }

  /**
   * Invalidate product-related cache
   */
  async invalidateProductCache(productId: string | number, additionalData?: any): Promise<void> {
    const patterns = [
      `product:*:*${productId}*`,
      `products:*`,
      'search:*',
      'featured:*'
    ];

    if (additionalData?.brand) {
      patterns.push(`products:brand:${additionalData.brand}:*`);
    }

    if (additionalData?.categoryId) {
      patterns.push(`category:${additionalData.categoryId}:*`);
    }

    // Invalidate all matching patterns
    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }
}