import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import NodeCache from 'node-cache';
import { CoreCachePlugin } from '../core-cache.plugin';

export interface CacheOptions {
  ttl?: number;
  useRedis?: boolean;
}

export interface CacheStats {
  hits: number;
  misses: number;
  keys: number;
  hitRate: number;
}

/**
 * Core Cache Service - Shared by all Vendure APIs
 * Provides Redis + in-memory fallback caching for all services
 */
@Injectable()
export class CoreCacheService implements OnModuleInit, OnModuleDestroy {
  private redis: Redis;
  private memoryCache: NodeCache;
  private stats = {
    hits: 0,
    misses: 0,
  };

  constructor() {
    this.memoryCache = new NodeCache({
      stdTTL: CoreCachePlugin.options.defaultTTL || 300,
      checkperiod: 60,
      useClones: false,
    });
  }

  async onModuleInit() {
    try {
      const redisOptions = CoreCachePlugin.options.redis || {};

      this.redis = new Redis({
        host: redisOptions.host || process.env.REDIS_HOST || 'localhost',
        port: redisOptions.port || parseInt(process.env.REDIS_PORT || '6379'),
        password: redisOptions.password || process.env.REDIS_PASSWORD,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        family: 4,
        keepAlive: true,
      });

      await this.redis.connect();
      console.log('✅ Core Cache Service: Redis connected successfully');
    } catch (error: any) {
      console.warn('⚠️ Core Cache Service: Redis connection failed, using in-memory cache only:', error?.message || error);
    }
  }

  async onModuleDestroy() {
    if (this.redis) {
      await this.redis.disconnect();
    }
    this.memoryCache.close();
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      if (this.redis && this.redis.status === 'ready') {
        const value = await this.redis.get(key);
        if (value !== null) {
          this.stats.hits++;
          return JSON.parse(value);
        }
      }

      const memValue = this.memoryCache.get<T>(key);
      if (memValue !== undefined) {
        this.stats.hits++;
        return memValue;
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      console.error('Core Cache get error:', error);
      this.stats.misses++;
      return null;
    }
  }

  async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    const { ttl = CoreCachePlugin.options.defaultTTL || 300, useRedis = true } = options;

    try {
      if (this.redis && this.redis.status === 'ready' && useRedis) {
        await this.redis.setex(key, ttl, JSON.stringify(value));
      }

      this.memoryCache.set(key, value, ttl);
      return true;
    } catch (error) {
      console.error('Core Cache set error:', error);
      return false;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      if (this.redis && this.redis.status === 'ready') {
        await this.redis.del(key);
      }
      this.memoryCache.del(key);
      return true;
    } catch (error) {
      console.error('Core Cache delete error:', error);
      return false;
    }
  }

  async delPattern(pattern: string): Promise<number> {
    let deletedCount = 0;

    try {
      if (this.redis && this.redis.status === 'ready') {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          deletedCount = await this.redis.del(...keys);
        }
      }

      const memKeys = this.memoryCache.keys().filter((key: string) =>
        new RegExp(pattern.replace(/\*/g, '.*')).test(key)
      );
      this.memoryCache.del(memKeys);

      return deletedCount + memKeys.length;
    } catch (error) {
      console.error('Core Cache pattern delete error:', error);
      return 0;
    }
  }

  async getOrSet<T>(
    key: string,
    fetchFn: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await fetchFn();
    await this.set(key, value, options);
    return value;
  }

  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      keys: this.memoryCache.keys().length,
      hitRate: total > 0 ? (this.stats.hits / total) * 100 : 0,
    };
  }

  async clear(): Promise<void> {
    try {
      if (this.redis && this.redis.status === 'ready') {
        await this.redis.flushdb();
      }
      this.memoryCache.flushAll();
    } catch (error) {
      console.error('Core Cache clear error:', error);
    }
  }

  isRedisAvailable(): boolean {
    return this.redis && this.redis.status === 'ready';
  }
}