import {
  PluginCommonModule,
  VendurePlugin,
  ProductService,
  ListQueryBuilder,
} from "@vendure/core";
import { ProductResolver } from "./shop-api/resolver";
import { shopApiExtensions } from "./shop-api/extension";

@VendurePlugin({
  imports: [PluginCommonModule],
  shopApiExtensions: {
    schema: shopApiExtensions,
    resolvers: [ProductResolver],
  },
  providers: [ProductService, ListQueryBuilder],
  compatibility: "3.3.5",
})
export class ProductPlugin {}
