import { Args, Query, Resolver, Info } from "@nestjs/graphql";
import {
  Allow,
  Ctx,
  RequestContext,
  Permission,
  Transaction,
  ProductService,
  TransactionalConnection,
  Product,
} from "@vendure/core";
import { CustomProductListOptions } from "../../../gql/generated";
import { GraphQLResolveInfo, FieldNode, SelectionNode } from "graphql";

// Helper function to check if a field is requested in the GraphQL query
function isFieldRequested(
  info: GraphQLResolveInfo,
  fieldPath: string
): boolean {
  const selectionSet = info.fieldNodes[0]?.selectionSet;
  if (!selectionSet) return false;

  const pathParts = fieldPath.split(".");
  let currentSelections = selectionSet.selections;

  for (const part of pathParts) {
    const found = currentSelections.find((selection: SelectionNode) => {
      return selection.kind === "Field" && selection.name.value === part;
    }) as FieldNode;

    if (!found) return false;

    if (found.selectionSet) {
      currentSelections = found.selectionSet.selections;
    }
  }

  return true;
}

@Resolver()
export class ProductResolver {
  constructor(
    private productService: ProductService,
    private connection: TransactionalConnection
  ) {}

  @Allow(Permission.Public)
  @Transaction()
  @Query()
  async customProducts(
    @Ctx() ctx: RequestContext,
    @Args("filters") filters: CustomProductListOptions,
    @Info() info: GraphQLResolveInfo
  ) {
    const qb = this.connection
      .getRepository(ctx, Product)
      .createQueryBuilder("product");

    // Join với bảng channels để filter theo channel hiện tại
    qb.leftJoin("product.channels", "productChannel");
    qb.andWhere("productChannel.id = :channelId", {
      channelId: ctx.channel.id,
    });

    // Always join translations table for name and slug filtering/sorting
    qb.leftJoinAndSelect("product.translations", "productTranslations");

    // Conditionally join relations based on requested fields
    if (isFieldRequested(info, "items.featuredAsset")) {
      qb.leftJoinAndSelect("product.featuredAsset", "featuredAsset");
    }

    if (isFieldRequested(info, "items.facetValues")) {
      qb.leftJoinAndSelect("product.facetValues", "facetValues");
      qb.leftJoinAndSelect("facetValues.facet", "facet");
      qb.leftJoinAndSelect(
        "facetValues.translations",
        "facetValueTranslations"
      );
    }

    if (isFieldRequested(info, "items.variants")) {
      qb.leftJoinAndSelect("product.variants", "variants");
      qb.leftJoinAndSelect("variants.translations", "variantTranslations");

      // Only join pricing if price fields are requested
      if (
        isFieldRequested(info, "items.variants.price") ||
        isFieldRequested(info, "items.variants.priceWithTax")
      ) {
        qb.leftJoinAndSelect("variants.productVariantPrices", "variantPrices");
      }

      // Only join custom fields if they're requested
      if (isFieldRequested(info, "items.variants.customFields")) {
        // Custom fields are automatically included with the entity
      }
    }

    qb.andWhere("product.enabled = :enabled", { enabled: true });

    // Chỉ lấy product có ít nhất một variant
    qb.andWhere(
      `EXISTS (
        SELECT 1 FROM product_variant pv
        WHERE pv."productId" = product.id
          AND pv."deletedAt" IS NULL
          AND pv.enabled = true
      )`
    );

    if (filters.filter?.id) {
      qb.andWhere("product.id = :id", { id: filters.filter.id });
    }
    if (filters.filter?.createdAt) {
      qb.andWhere("product.createdAt = :createdAt", {
        createdAt: filters.filter.createdAt,
      });
    }
    if (filters.filter?.facetValueIds) {
      // Only join facetValues if not already joined
      if (!isFieldRequested(info, "items.facetValues")) {
        qb.leftJoin("product.facetValues", "facetValues");
      }
      qb.andWhere("facetValues.id IN (:...facetValueIds)", {
        facetValueIds: filters.filter.facetValueIds,
      });
    }
    if (filters.filter?.categoryIds) {
      qb.andWhere(
        `product.id IN (
          SELECT DISTINCT pv."productId" FROM collection_product_variants_product_variant pc 
          JOIN product_variant pv ON pv.id = pc."productVariantId" 
          WHERE pc."collectionId" IN (:...categoryIds)
        )`,
        {
          categoryIds: filters.filter.categoryIds,
        }
      );
    }
    if (filters.filter?.name) {
      qb.andWhere("productTranslations.name ILIKE :name", {
        name: `%${filters.filter.name}%`,
      });
    }
    if (filters.filter?.slug) {
      qb.andWhere("productTranslations.slug ILIKE :slug", {
        slug: `%${filters.filter.slug}%`,
      });
    }
    if (filters.filter?.price) {
      // Join with ProductVariant and ProductVariantPrice to access price information
      if (!isFieldRequested(info, "items.variants")) {
        qb.leftJoin("product.variants", "variant");
        qb.leftJoin("variant.productVariantPrices", "variantPrice");
      } else {
        // Use the already joined variants alias
        qb.leftJoin("variants.productVariantPrices", "variantPrice");
      }

      const priceFilter = filters.filter.price;

      // Equal to specific price
      if (priceFilter.eq !== undefined) {
        qb.andWhere("variantPrice.price = :priceEq", {
          priceEq: priceFilter.eq,
        });
      }

      // Less than specific price
      if (priceFilter.lt !== undefined) {
        qb.andWhere("variantPrice.price < :priceLt", {
          priceLt: priceFilter.lt,
        });
      }

      // Less than or equal to specific price
      if (priceFilter.lte !== undefined) {
        qb.andWhere("variantPrice.price <= :priceLte", {
          priceLte: priceFilter.lte,
        });
      }

      // Greater than specific price
      if (priceFilter.gt !== undefined) {
        qb.andWhere("variantPrice.price > :priceGt", {
          priceGt: priceFilter.gt,
        });
      }

      // Greater than or equal to specific price
      if (priceFilter.gte !== undefined) {
        qb.andWhere("variantPrice.price >= :priceGte", {
          priceGte: priceFilter.gte,
        });
      }

      // Between two prices (range)
      if (priceFilter.between !== undefined && priceFilter.between !== null) {
        qb.andWhere(
          "variantPrice.price >= :priceStart AND variantPrice.price <= :priceEnd",
          {
            priceStart: priceFilter.between.start,
            priceEnd: priceFilter.between.end,
          }
        );
      }

      // Is null check
      if (priceFilter.isNull !== undefined) {
        if (priceFilter.isNull) {
          qb.andWhere("variantPrice.price IS NULL");
        } else {
          qb.andWhere("variantPrice.price IS NOT NULL");
        }
      }
    }
    if (filters.sort) {
      if (filters.sort.price) {
        // Ensure variant prices are joined for sorting
        if (!isFieldRequested(info, "items.variants")) {
          qb.leftJoin("product.variants", "variant");
          qb.leftJoin("variant.productVariantPrices", "variantPrice");
        }
        qb.orderBy("variantPrice.price", filters.sort.price);
      }
      if (filters.sort.createdAt) {
        qb.orderBy("product.createdAt", filters.sort.createdAt);
      }
      if (filters.sort.name) {
        qb.orderBy("productTranslations.name", filters.sort.name);
      }
    }

    // Filter by current language context if available
    if (ctx.languageCode) {
      qb.andWhere("productTranslations.languageCode = :languageCode", {
        languageCode: ctx.languageCode,
      });
    }
    if (filters.skip) {
      qb.skip(filters.skip);
    }
    if (filters.take) {
      qb.take(filters.take);
    } else {
      qb.take(10);
    }
    // console.log(1111, qb.getQueryAndParameters());

    const [products, totalItems] = await qb.getManyAndCount();
    return {
      items: products,
      totalItems,
    };
  }
}
