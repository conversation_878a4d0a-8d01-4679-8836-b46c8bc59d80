import { Args, Query, Resolver } from "@nestjs/graphql";
import {
  Allow,
  Ctx,
  RequestContext,
  Permission,
  Transaction,
  ProductService,
  TransactionalConnection,
  Product,
} from "@vendure/core";
import { CustomProductListOptions } from "../../../gql/generated";

@Resolver()
export class ProductResolver {
  constructor(
    private productService: ProductService,
    private connection: TransactionalConnection
  ) {}

  @Allow(Permission.Public)
  @Transaction()
  @Query()
  async customProducts(
    @Ctx() ctx: RequestContext,
    @Args("filters") filters: CustomProductListOptions
  ) {
    const qb = this.connection
      .getRepository(ctx, Product)
      .createQueryBuilder("product");

    // Join translations table for name and slug filtering/sorting
    qb.leftJoinAndSelect("product.translations", "productTranslations");

    qb.andWhere("product.enabled = :enabled", { enabled: true });

    if (filters.filter?.id) {
      qb.andWhere("product.id = :id", { id: filters.filter.id });
    }
    if (filters.filter?.createdAt) {
      qb.andWhere("product.createdAt = :createdAt", {
        createdAt: filters.filter.createdAt,
      });
    }
    if (filters.filter?.facetIds) {
      qb.leftJoinAndSelect("product.facetValues", "facetValues");
      qb.andWhere("facetValues.facetId IN (:...facetIds)", {
        facetIds: filters.filter.facetIds,
      });
    }
    if (filters.filter?.categoryIds) {
      qb.leftJoinAndSelect("product.categories", "categories");
      qb.andWhere("categories.id IN (:...categoryIds)", {
        categoryIds: filters.filter.categoryIds,
      });
    }
    if (filters.filter?.name) {
      qb.andWhere("productTranslations.name ILIKE :name", {
        name: `%${filters.filter.name}%`,
      });
    }
    if (filters.filter?.slug) {
      qb.andWhere("productTranslations.slug ILIKE :slug", {
        slug: `%${filters.filter.slug}%`,
      });
    }
    if (filters.filter?.price) {
      // Join with ProductVariant and ProductVariantPrice to access price information
      qb.leftJoin("product.variants", "variant");
      qb.leftJoin("variant.productVariantPrices", "variantPrice");

      const priceFilter = filters.filter.price;

      // Equal to specific price
      if (priceFilter.eq !== undefined) {
        qb.andWhere("variantPrice.price = :priceEq", {
          priceEq: priceFilter.eq,
        });
      }

      // Less than specific price
      if (priceFilter.lt !== undefined) {
        qb.andWhere("variantPrice.price < :priceLt", {
          priceLt: priceFilter.lt,
        });
      }

      // Less than or equal to specific price
      if (priceFilter.lte !== undefined) {
        qb.andWhere("variantPrice.price <= :priceLte", {
          priceLte: priceFilter.lte,
        });
      }

      // Greater than specific price
      if (priceFilter.gt !== undefined) {
        qb.andWhere("variantPrice.price > :priceGt", {
          priceGt: priceFilter.gt,
        });
      }

      // Greater than or equal to specific price
      if (priceFilter.gte !== undefined) {
        qb.andWhere("variantPrice.price >= :priceGte", {
          priceGte: priceFilter.gte,
        });
      }

      // Between two prices (range)
      if (priceFilter.between !== undefined && priceFilter.between !== null) {
        qb.andWhere(
          "variantPrice.price >= :priceStart AND variantPrice.price <= :priceEnd",
          {
            priceStart: priceFilter.between.start,
            priceEnd: priceFilter.between.end,
          }
        );
      }

      // Is null check
      if (priceFilter.isNull !== undefined) {
        if (priceFilter.isNull) {
          qb.andWhere("variantPrice.price IS NULL");
        } else {
          qb.andWhere("variantPrice.price IS NOT NULL");
        }
      }
    }
    if (filters.sort) {
      if (filters.sort.price) {
        qb.orderBy("variantPrice.price", filters.sort.price);
      }
      if (filters.sort.createdAt) {
        qb.orderBy("product.createdAt", filters.sort.createdAt);
      }
      if (filters.sort.name) {
        qb.orderBy("productTranslations.name", filters.sort.name);
      }
    }

    // Filter by current language context if available
    if (ctx.languageCode) {
      qb.andWhere("productTranslations.languageCode = :languageCode", {
        languageCode: ctx.languageCode,
      });
    }

    const [products, totalItems] = await qb.getManyAndCount();
    return {
      items: products,
      totalItems,
    };
  }
}
