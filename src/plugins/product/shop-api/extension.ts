import gql from "graphql-tag";

export const shopApiExtensions = gql`
  input CustomProductFilterParameter {
    id: IDOperators
    createdAt: DateOperators
    name: StringOperators
    categoryIds: [ID!]
    facetIds: [ID!]
    price: NumberOperators
    _and: [CustomProductFilterParameter!]
    _or: [CustomProductFilterParameter!]
  }

  input CustomProductSortParameter {
    createdAt: SortOrder
    name: SortOrder
    price: SortOrder
  }

  input CustomProductListOptions {
    skip: Int
    take: Int
    sort: CustomProductSortParameter
    filter: CustomProductFilterParameter
  }
  type CustomProductResult {
    items: [Product!]!
    totalItems: Int!
  }

  extend type Query {
    customProducts(filters: CustomProductListOptions!): CustomProductResult!
  }
`;
