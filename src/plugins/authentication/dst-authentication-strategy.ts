import fetch from "node-fetch";
import {
    AuthenticationStrategy,
    ExternalAuthenticationService,
    Injector,
    RequestContext,
    User,
} from "@vendure/core";
import { DocumentNode } from "graphql";
import gql from "graphql-tag";

export type DstAuthData = {
    phone: string;
    password: string;
};
export class DstAuthenticationStrategy
    implements AuthenticationStrategy<DstAuthData>
{
    readonly name = "dst";
    private externalAuthenticationService: ExternalAuthenticationService;

    init(injector: Injector) {
        this.externalAuthenticationService = injector.get(
            ExternalAuthenticationService
        );
    }

    defineInputType(): DocumentNode {
        return gql`
            input EventistaAuthInput {
                email: String
                password: String!
                phone: String
                platform: String
            }
        `;
    }
    async authenticate(
        ctx: RequestContext,
        data: DstAuthData
    ): Promise<User | false> {
        // verify email and password by eventista sso
        const url = `${process.env.DST_SSO}/sso/api/auth/login`;
        const response = await fetch(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify(data),
        });

        const isSuccess = [200, 201].includes(response.status);
        if (!isSuccess) return false;

        const result = await response.json();

        if (result?.errorCode !== 0) {
            return false;
        }
        const accessToken = result.data.accessToken;
        const { data: externalUser } = await fetch(
            `${process.env.DST_SSO}/sso/api/users/me`,
            {
                method: "GET",
                headers: {
                    Authorization: "Bearer " + accessToken,
                },
            }
        ).then((res) => res.json());

        const user = await this.externalAuthenticationService.findCustomerUser(
            ctx,
            this.name,
            externalUser.phone || externalUser.email,
            false
        );
        if (user) {
            return user;
        }

        const res = ctx.req?.res;
        if (res) {
            res.cookie("NEXT_SSO_SESSION", accessToken, {
                httpOnly: false,
                sameSite: "lax",
                // secure: true,
            });
        }

        return this.externalAuthenticationService.createCustomerAndUser(ctx, {
            strategy: this.name,
            externalIdentifier: externalUser.id,
            emailAddress: externalUser.email || externalUser.phone,
            firstName: externalUser.name || "",
            lastName: "",
            verified: true,
        });
    }
}
