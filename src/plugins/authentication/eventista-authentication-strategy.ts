import fetch from "node-fetch";
import {
    AuthenticationStrategy,
    ExternalAuthenticationService,
    Injector,
    RequestContext,
    User,
} from "@vendure/core";
import { DocumentNode } from "graphql";
import gql from "graphql-tag";

export type EventistaAuthData = {
    email: string;
    password: string;
};

export class EventistaAuthenticationStrategy
    implements AuthenticationStrategy<EventistaAuthData>
{
    readonly name = "eventista";
    private externalAuthenticationService: ExternalAuthenticationService;

    init(injector: Injector) {
        this.externalAuthenticationService = injector.get(
            ExternalAuthenticationService
        );
    }

    defineInputType(): DocumentNode {
        return gql`
            input EventistaAuthInput {
                email: String!
                password: String!
            }
        `;
    }

    async authenticate(
        ctx: RequestContext,
        data: EventistaAuthData
    ): Promise<User | false> {
        // verify email and password by eventista sso
        const url = `${process.env.EVENTISTA_SSO}/v1/auth/login`;
        const result = await fetch(url, {
            method: "POST",
            body: JSON.stringify(data),
        }).then((res) => res.json());

        if (result?.errorCode !== 0) {
            // throw new Error(result?.message || "Invalid credentials");
            return false;
        }
        const userInfo = result.data.user;
        const user = await this.externalAuthenticationService.findCustomerUser(
            ctx,
            this.name,
            userInfo.email
        );
        if (user) {
            return user;
        }

        return this.externalAuthenticationService.createCustomerAndUser(ctx, {
            strategy: this.name,
            externalIdentifier: userInfo.email,
            emailAddress: userInfo.email,
            firstName: userInfo.name,
            lastName: "",
            verified: userInfo.verified,
        });
    }
}
