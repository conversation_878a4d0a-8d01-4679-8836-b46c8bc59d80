import { Injectable } from '@nestjs/common';
import {
  CacheService,
  RequestContextCacheService,
  OrderService,
  RequestContext,
  Order,
  ID
} from '@vendure/core';

/**
 * Enhanced Order Service - Uses Vendure's official caching system
 * This demonstrates the CORRECT way to use Vendure's caching
 */
@Injectable()
export class EnhancedOrderService {
  constructor(
    private cacheService: CacheService,
    private requestContextCache: RequestContextCacheService,
    private orderService: OrderService
  ) {}

  /**
   * Get order by ID with proper Vendure caching
   */
  async findOne(ctx: RequestContext, orderId: ID): Promise<Order | undefined> {
    // Use request-context cache for per-request caching
    return this.requestContextCache.get(ctx, `order:${orderId}`, async () => {
      // Create a persistent cache for this specific operation
      const orderCache = this.cacheService.createCache({
        getKey: (id: string | number) => `order:${id}`,
        options: {
          ttl: 300, // 5 minutes
          tags: ['order', `order:${orderId}`]
        }
      });

      // Get from persistent cache or database
      return orderCache.get(orderId, async () => {
        return this.orderService.findOne(ctx, orderId);
      });
    });
  }

  /**
   * Get order by code with caching
   */
  async findOneByCode(ctx: RequestContext, orderCode: string): Promise<Order | undefined> {
    return this.requestContextCache.get(ctx, `order-code:${orderCode}`, async () => {
      const orderCodeCache = this.cacheService.createCache({
        getKey: (code: string | number) => `order-code:${code}`,
        options: {
          ttl: 300,
          tags: ['order', `order-code:${orderCode}`]
        }
      });

      return orderCodeCache.get(orderCode, async () => {
        return this.orderService.findOneByCode(ctx, orderCode);
      });
    });
  }

  /**
   * Invalidate order cache when order changes
   */
  async invalidateOrderCache(orderId: ID): Promise<void> {
    await this.cacheService.invalidateTags([
      'order',
      `order:${orderId}`,
      `order-code:*` // This would need pattern matching, but tags are better
    ]);
  }

  /**
   * Example of how to use caching in your own methods
   */
  async getOrderSummary(ctx: RequestContext, orderId: ID): Promise<any> {
    return this.requestContextCache.get(ctx, `order-summary:${orderId}`, async () => {
      const summaryCache = this.cacheService.createCache({
        getKey: (id: string | number) => `order-summary:${id}`,
        options: {
          ttl: 180, // 3 minutes
          tags: ['order', 'order-summary', `order:${orderId}`]
        }
      });

      return summaryCache.get(orderId, async () => {
        // Your custom business logic here
        const order = await this.orderService.findOne(ctx, orderId);
        if (!order) return null;

        return {
          id: order.id,
          code: order.code,
          state: order.state,
          total: order.total,
          itemCount: order.lines.length,
          // ... other summary data
        };
      });
    });
  }
}