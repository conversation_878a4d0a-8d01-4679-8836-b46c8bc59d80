import gql from "graphql-tag";

export const shopApiExtensions = gql`
  input CheckoutInput {
    domain: String!
    paymentMethod: String!
  }
  
  input CancelOrderInput {
    orderCode: String!
    reason: String
  }
  
  type CheckoutPaymentError {
    message: String!
    errorCode: String!
  }
  type CheckoutOrderError {
    message: String!
    errorCode: String!
  }
  type Checkout {
    zaloUrl: String
    orderStatus: String
    orderCode: String
  }
  
  type CancelOrderSuccess {
    success: Boolean!
    message: String!
    orderCode: String!
    state: String!
  }
  
  type CancelOrderError {
    success: Boolean!
    message: String!
    errorCode: String!
  }
  
  union CheckoutResult = Checkout | CheckoutPaymentError | CheckoutOrderError
  union CancelOrderResult = CancelOrderSuccess | CancelOrderError
  
  extend type Mutation {
    checkout(input: CheckoutInput!): CheckoutResult!
    cancelOrder(input: CancelOrderInput!): CancelOrderResult!
  }
  extend type Query {
    getOrderStateByCode(code: String!): String!
    getOrdersHistory: [Order]!
  }
`;
