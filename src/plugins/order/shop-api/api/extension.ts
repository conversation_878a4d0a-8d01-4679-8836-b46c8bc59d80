import gql from "graphql-tag";

export const shopApiExtensions = gql`
  input CheckoutInput {
    domain: String!
    paymentMethod: String!
  }
  type CheckoutPaymentError {
    message: String!
    errorCode: String!
  }
  type CheckoutOrderError {
    message: String!
    errorCode: String!
  }
  type Checkout {
    zaloUrl: String
    orderStatus: String
    orderCode: String
  }
  union CheckoutResult = Checkout | CheckoutPaymentError | CheckoutOrderError
  extend type Mutation {
    checkout(input: CheckoutInput!): CheckoutResult!
  }
  extend type Query {
    getOrderStateByCode(code: String!): String!
    getOrdersHistory: [Order]!
  }
`;
