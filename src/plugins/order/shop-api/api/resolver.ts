import { Args, Mutation, Resolver, Query } from "@nestjs/graphql";
import {
  Ctx,
  Customer,
  Order,
  OrderService,
  Payment,
  PaymentService,
  RequestContext,
  StockLocationService,
  Transaction,
  TransactionalConnection,
} from "@vendure/core";
import {
  CheckoutResult,
  QueryGetOrderStateByCodeArgs,
  type MutationCheckoutArgs,
} from "../../../../../src/gql/generated";
import hmacSHA256 from "crypto-js/hmac-sha256";
import { format } from "date-fns";
import { ViettelProvider } from "../../../../plugins/shipping/providers/viettel";
import { getSecretKey } from "../../../../utils/getSecretKey";

@Resolver()
export class OrderResolver {
  constructor(
    private orderService: OrderService,
    private paymentService: PaymentService,
    private connection: TransactionalConnection,
    private stockLocationService: StockLocationService
  ) {}
  @Transaction()
  @Mutation()
  async checkout(
    @Ctx() ctx: RequestContext,
    @Args() args: MutationCheckoutArgs
  ): Promise<CheckoutResult> {
    if (
      !["zalopay", "zalopay_cc", "zalopay_vietqr", "cod"].includes(
        args.input.paymentMethod
      )
    ) {
      return {
        __typename: "CheckoutPaymentError",
        errorCode: "10001",
        message: "Payment method is not valid",
      };
    }
    if (!ctx.session?.activeOrderId) {
      return {
        __typename: "CheckoutOrderError",
        errorCode: "20001",
        message: "Order not found from session",
      };
    }
    const secretKey = await getSecretKey();
    const viettelProvider = new ViettelProvider(this.connection, secretKey);
    let order = await this.orderService.findOne(
      ctx,
      ctx.session?.activeOrderId
    );
    if (!order) {
      return {
        __typename: "CheckoutOrderError",
        errorCode: "20002",
        message: "Order not found",
      };
    }
    if (order.lines.length === 0) {
      return {
        __typename: "CheckoutOrderError",
        errorCode: "20003",
        message: "Order has no items",
      };
    }
    const address = viettelProvider.getReceiverAddress(order);
    const stocks = await this.stockLocationService.findAll(ctx);

    if (stocks.totalItems === 0) {
      return {
        __typename: "CheckoutOrderError",
        errorCode: "20003",
        message: "Order has no items",
      };
    }
    const stock = stocks.items[0];
    const shipping = await viettelProvider.calculateShipping(ctx, {
      senderAddress: stock.customFields.address ?? "",
      receiverAddress: address,
      productPrice: order.subTotal,
    });
    if (shipping > 0) {
      await this.connection.getRepository(ctx, Order).update(order.id, {
        shipping: shipping,
      });
      order = await this.orderService.findOne(ctx, ctx.session?.activeOrderId);
    }
    if (!order) {
      return {
        __typename: "CheckoutOrderError",
        errorCode: "20002",
        message: "Order not found",
      };
    }

    let payment = await this.connection.getRepository(ctx, Payment).findOne({
      where: {
        order: {
          id: ctx.session?.activeOrderId,
        },
      },
    });
    if (!payment) {
      payment = await this.paymentService.create(ctx, {
        order: {
          id: ctx.session?.activeOrderId,
        },
        method: args.input.paymentMethod,
        amount: order.subTotal,
        state: "Created",
        metadata: {},
      });
    }

    if (args.input.paymentMethod === "cod") {
      try {
        await this.connection.getRepository(ctx, Payment).update(payment.id, {
          state: "Authorized",
        });

        await this.connection.getRepository(ctx, Order).update(order.id, {
          state: "PaymentAuthorized",
          active: false,
        });

        const updatedOrder = await this.orderService.findOne(ctx, order.id);

        return {
          __typename: "Checkout",
          zaloUrl: null,
          orderStatus: updatedOrder?.state,
          orderCode: updatedOrder?.code,
        };
      } catch (error) {
        console.error("COD checkout error:", error);
        return {
          __typename: "CheckoutPaymentError",
          errorCode: "10006",
          message: `COD checkout failed: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        };
      }
    }

    const callbackUrl = process.env.ZALO_CALLBACK_URL;
    const zaloUrl = process.env.ZALO_URL;
    const appId = process.env.ZALO_APP_ID;
    const macKey = process.env.ZALO_MAC_KEY || "";
    const { code, total } = order;

    const domain = args.input.domain || "faniesta.com";
    const redirectUrl = `https://${domain}/thanh-toan/ket-qua`;
    const transId = `${format(new Date(), "yyMMdd")}_${code}`;
    const description = `Eventista - Thanh toán merchandise, Mã giao dịch: ${code}`;
    let preferredPaymentMethod = "";
    switch (args.input.paymentMethod) {
      case "zalopay_vietqr":
        preferredPaymentMethod = "vietqr";
        break;
      case "zalopay_cc":
        preferredPaymentMethod = "international_card";
        break;
      case "zalopay":
      default:
        preferredPaymentMethod = "zalopay_wallet";
        break;
    }

    const config = {
      app_id: Number(appId),
      app_user: "Eventista",
      app_time: Date.now(),
      expire_duration_seconds: 600,
      amount: total,
      app_trans_id: transId,
      bank_code: "",
      embed_data: JSON.stringify({
        preferred_payment_method: [preferredPaymentMethod],
        redirecturl: redirectUrl + `?orderCode=${code}`,
      }),
      item: JSON.stringify(order.lines.map((line) => line.productVariantId)),
      callback_url: callbackUrl,
      description,
    };

    const rawSignature = [
      config.app_id,
      config.app_trans_id,
      config.app_user,
      config.amount,
      config.app_time,
      config.embed_data,
      config.item,
    ].join("|");
    const mac = hmacSHA256(rawSignature, macKey).toString();

    // json object send to ZaloPay endpoint
    const body = {
      ...config,
      mac,
    };
    const result = await fetch(`${zaloUrl}/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });
    const res = await result.json();

    if (res.return_code === 1) {
      await this.connection.getRepository(ctx, Order).update(order.id, {
        state: "PaymentAuthorized",
        active: false,
      });
      await this.connection.getRepository(ctx, Payment).update(payment.id, {
        state: "Authorized",
        metadata: {
          zaloUrl: res.order_url,
        },
      });
      return {
        __typename: "Checkout",
        zaloUrl: res.order_url,
        orderStatus: null,
        orderCode: null,
      };
    }
    console.log(55555, res);
    await this.connection.getRepository(ctx, Payment).update(payment.id, {
      state: "Declined",
      errorMessage: res.status,
    });
    return {
      __typename: "CheckoutPaymentError",
      errorCode: "10002",
      message: "Create payment failed",
    };
  }

  @Query()
  async getOrderStateByCode(
    @Ctx() ctx: RequestContext,
    @Args() args: QueryGetOrderStateByCodeArgs
  ) {
    const order = await this.orderService.findOneByCode(ctx, args.code);
    return order?.state;
  }

  @Query()
  async getOrdersHistory(@Ctx() ctx: RequestContext) {
    if (!ctx.session?.user?.id) {
      return [];
    }
    const customer = await this.connection
      .getRepository(ctx, Customer)
      .findOne({
        where: {
          user: {
            id: ctx.session?.user.id,
          },
        },
      });
    if (!customer) {
      return [];
    }

    const paymentSettledOrders = await this.connection
      .getRepository(ctx, Order)
      .find({
        where: {
          customerId: customer.id,
          state: "PaymentSettled",
        },
        order: {
          createdAt: "DESC",
        },
        relations: ["lines", "payments"],
      });

    const codOrders = await this.connection.getRepository(ctx, Order).find({
      where: {
        customerId: customer.id,
        state: "PaymentAuthorized",
        payments: {
          method: "cod",
        },
      },
      order: {
        createdAt: "DESC",
      },
      relations: ["lines", "payments"],
    });

    const allOrders = [...paymentSettledOrders, ...codOrders];
    return allOrders.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
  }
}
