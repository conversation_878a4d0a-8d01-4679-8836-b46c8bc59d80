import {
  PluginCommonModule,
  VendurePlugin,
  CustomerService,
  OrderService,
  PaymentService,
} from "@vendure/core";
import { OrderResolver } from "./api/resolver";
import { shopApiExtensions } from "./api/extension";
@VendurePlugin({
  imports: [PluginCommonModule],
  shopApiExtensions: {
    schema: shopApiExtensions,
    resolvers: [OrderResolver],
  },
  providers: [CustomerService, OrderService, PaymentService],
  compatibility: "3.3.5",
})
export class OrderPlugin {}
