import {
  PluginCommonModule,
  VendurePlugin,
  CustomerService,
  OrderService,
  PaymentService,
} from "@vendure/core";
import { OrderResolver } from "./api/resolver";
import { shopApiExtensions } from "./api/extension";
import { CustomEmailPlugin } from "../../email";

@VendurePlugin({
  imports: [PluginCommonModule, CustomEmailPlugin],
  shopApiExtensions: {
    schema: shopApiExtensions,
    resolvers: [OrderResolver],
  },
  providers: [CustomerService, OrderService, PaymentService],
  compatibility: "3.3.5",
})
export class OrderPlugin {}
