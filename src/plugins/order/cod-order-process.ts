import { OrderProcess } from "@vendure/core";

export const codOrderProcess: OrderProcess<never> = {
  transitions: {
    PaymentAuthorized: {
      to: ["PartiallyShipped", "Shipped", "Cancelled"],
    },
  },

  onTransitionStart: async (fromState, toState, data) => {
    const { order } = data;
        
    // Only allow direct fulfillment transitions for COD payment method
    if (fromState === "PaymentAuthorized" && ["PartiallyShipped", "Shipped"].includes(toState)) {
      const payment = order.payments?.find(p => p.method === "cod");
      if (!payment) {
        return "Only COD orders can be fulfilled from PaymentAuthorized state";
      }
    }
  },
}; 