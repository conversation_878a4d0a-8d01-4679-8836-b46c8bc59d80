import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import {
  CacheService,
  OrderStateTransitionEvent,
  ProductEvent,
  CustomerEvent,
  AssetEvent
} from '@vendure/core';

/**
 * Cache Event Handler - Handles cache invalidation based on Vendure events
 * This demonstrates the CORRECT way to invalidate cache using Vendure's tag system
 */
@Injectable()
export class CacheEventHandler {
  constructor(private cacheService: CacheService) {}

  /**
   * Invalidate order cache when order state changes
   */
  @OnEvent(OrderStateTransitionEvent)
  async onOrderStateTransition(event: OrderStateTransitionEvent) {
    console.log(`🔄 Cache: Invalidating order cache for order ${event.order.id} (${event.fromState} → ${event.toState})`);

    await this.cacheService.invalidateTags([
      'order',
      `order:${event.order.id}`,
      `order-code:${event.order.code}`,
      'customer-orders',
      `customer:${event.order.customer?.id}`,
    ]);
  }

  /**
   * Invalidate product cache when product changes
   */
  @OnEvent(ProductEvent)
  async onProductEvent(event: ProductEvent) {
    console.log(`🔄 Cache: Invalidating product cache for product ${event.entity.id} (${event.type})`);

    await this.cacheService.invalidateTags([
      'product',
      `product:${event.entity.id}`,
      `product-slug:${event.entity.slug}`,
      'featured-products', // Invalidate featured products list
    ]);
  }

  /**
   * Invalidate customer cache when customer changes
   */
  @OnEvent(CustomerEvent)
  async onCustomerEvent(event: CustomerEvent) {
    console.log(`🔄 Cache: Invalidating customer cache for customer ${event.entity.id} (${event.type})`);

    await this.cacheService.invalidateTags([
      'customer',
      `customer:${event.entity.id}`,
      'customer-orders', // Customer changes might affect order history
    ]);
  }

  /**
   * Invalidate asset-related cache when assets change
   */
  @OnEvent(AssetEvent)
  async onAssetEvent(event: AssetEvent) {
    console.log(`🔄 Cache: Invalidating asset cache for asset ${event.entity.id} (${event.type})`);

    // Assets are used in products, so invalidate product cache
    await this.cacheService.invalidateTags([
      'product', // Products use assets
      'featured-products', // Featured products show assets
    ]);
  }

  /**
   * Manual cache invalidation method for custom use cases
   */
  async invalidateByTags(tags: string[]) {
    console.log(`🔄 Cache: Manual invalidation for tags: ${tags.join(', ')}`);
    await this.cacheService.invalidateTags(tags);
  }

  /**
   * Clear all cache (use with caution)
   */
  async clearAllCache() {
    console.log('🔄 Cache: Clearing ALL cache');
    // Note: Vendure doesn't have a direct "clear all" method
    // We'd need to implement this by tracking all possible tags
    await this.cacheService.invalidateTags([
      'order',
      'product',
      'customer',
      'featured-products',
      'customer-orders'
    ]);
  }
}