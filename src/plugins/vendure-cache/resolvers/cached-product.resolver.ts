import { Args, Query, Resolver } from '@nestjs/graphql';
import {
  Ctx,
  RequestContext,
  CacheService,
  RequestContextCacheService,
  ProductService,
  ID
} from '@vendure/core';

/**
 * Cached Product Resolver - Demonstrates proper Vendure caching for products
 */
@Resolver()
export class CachedProductResolver {
  constructor(
    private cacheService: CacheService,
    private requestContextCache: RequestContextCacheService,
    private productService: ProductService
  ) {}

  /**
   * Get product with proper multi-level caching
   */
  @Query()
  async cachedProduct(
    @Ctx() ctx: RequestContext,
    @Args('id') id: ID
  ) {
    // Level 1: Request-context cache
    return this.requestContextCache.get(ctx, `product:${id}`, async () => {

      // Level 2: Persistent cache (Redis)
      const productCache = this.cacheService.createCache({
        getKey: (productId: string | number) => `product:${productId}`,
        options: {
          ttl: 900, // 15 minutes (products change less frequently)
          tags: ['product', `product:${id}`]
        }
      });

      return productCache.get(id, async () => {
        // Level 3: Database
        const product = await this.productService.findOne(ctx, id);

        if (!product) return null;

        // Return serializable data
        return {
          id: product.id,
          name: product.name,
          slug: product.slug,
          description: product.description,
          enabled: product.enabled,
          featuredAsset: product.featuredAsset ? {
            id: product.featuredAsset.id,
            preview: product.featuredAsset.preview,
            source: product.featuredAsset.source,
          } : null,
          variants: product.variants.map(variant => ({
            id: variant.id,
            name: variant.name,
            sku: variant.sku,
            price: variant.price,
            priceWithTax: variant.priceWithTax,
            stockLevel: variant.stockLevel,
            enabled: variant.enabled,
          })),
          createdAt: product.createdAt.toISOString(),
          updatedAt: product.updatedAt.toISOString(),
        };
      });
    });
  }

  /**
   * Get featured products with caching
   */
  @Query()
  async cachedFeaturedProducts(
    @Ctx() ctx: RequestContext,
    @Args('take', { defaultValue: 12 }) take: number
  ) {
    const cacheKey = `featured-products:${take}`;

    return this.requestContextCache.get(ctx, cacheKey, async () => {
      const featuredProductsCache = this.cacheService.createCache({
        getKey: (key: string | number) => `featured-products:${key}`,
        options: {
          ttl: 600, // 10 minutes
          tags: ['product', 'featured-products']
        }
      });

      return featuredProductsCache.get(cacheKey, async () => {
        const result = await this.productService.findAll(ctx, {
          take,
          skip: 0,
          filter: {
            enabled: { eq: true }
          }
        });

        return {
          items: result.items.map((product: any) => ({
            id: product.id,
            name: product.name,
            slug: product.slug,
            featuredAsset: product.featuredAsset ? {
              id: product.featuredAsset.id,
              preview: product.featuredAsset.preview,
            } : null,
            variants: product.variants.slice(0, 1).map((variant: any) => ({
              id: variant.id,
              price: variant.price,
              priceWithTax: variant.priceWithTax,
            })),
          })),
          totalItems: result.totalItems,
        };
      });
    });
  }
}