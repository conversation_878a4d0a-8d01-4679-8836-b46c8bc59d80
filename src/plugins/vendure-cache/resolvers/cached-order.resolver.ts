import { Args, Query, Resolver } from '@nestjs/graphql';
import {
  Ctx,
  RequestContext,
  CacheService,
  RequestContextCacheService,
  OrderService,
  ID
} from '@vendure/core';

/**
 * Cached Order Resolver - Demonstrates proper Vendure caching
 * This shows the CORRECT way to implement caching in GraphQL resolvers
 */
@Resolver()
export class CachedOrderResolver {
  constructor(
    private cacheService: CacheService,
    private requestContextCache: RequestContextCacheService,
    private orderService: OrderService
  ) {}

  /**
   * Get order with proper multi-level caching
   * Level 1: Request-context cache (per-request, prevents duplicate queries)
   * Level 2: Persistent cache (Redis, shared across requests)
   * Level 3: Database (only if not cached)
   */
  @Query()
  async cachedOrder(
    @Ctx() ctx: RequestContext,
    @Args('id') id: ID
  ) {
    // Level 1: Request-context cache (fastest, per-request only)
    return this.requestContextCache.get(ctx, `order:${id}`, async () => {

      // Level 2: Persistent cache (Redis)
      const orderCache = this.cacheService.createCache({
        getKey: (orderId: string | number) => `order:${orderId}`,
        options: {
          ttl: 300, // 5 minutes
          tags: ['order', `order:${id}`]
        }
      });

      return orderCache.get(id, async () => {
        // Level 3: Database (slowest, only if not cached)
        const order = await this.orderService.findOne(ctx, id);

        if (!order) return null;

        // Return serializable data (important for caching)
        return {
          id: order.id,
          code: order.code,
          state: order.state,
          total: order.total,
          totalWithTax: order.totalWithTax,
          currencyCode: order.currencyCode,
          customer: order.customer ? {
            id: order.customer.id,
            firstName: order.customer.firstName,
            lastName: order.customer.lastName,
            emailAddress: order.customer.emailAddress,
          } : null,
          lines: order.lines.map(line => ({
            id: line.id,
            quantity: line.quantity,
            unitPrice: line.unitPrice,
            unitPriceWithTax: line.unitPriceWithTax,
            linePrice: line.linePrice,
            productVariant: {
              id: line.productVariant.id,
              name: line.productVariant.name,
              sku: line.productVariant.sku,
            }
          })),
          createdAt: order.createdAt.toISOString(),
          updatedAt: order.updatedAt.toISOString(),
        };
      });
    });
  }

  /**
   * Get order by code with caching
   */
  @Query()
  async cachedOrderByCode(
    @Ctx() ctx: RequestContext,
    @Args('code') code: string
  ) {
    return this.requestContextCache.get(ctx, `order-code:${code}`, async () => {
      const orderCodeCache = this.cacheService.createCache({
        getKey: (orderCode: string | number) => `order-code:${orderCode}`,
        options: {
          ttl: 300,
          tags: ['order', `order-code:${code}`]
        }
      });

      return orderCodeCache.get(code, async () => {
        const order = await this.orderService.findOneByCode(ctx, code);

        if (!order) return null;

        // Return the same serializable format
        return {
          id: order.id,
          code: order.code,
          state: order.state,
          total: order.total,
          totalWithTax: order.totalWithTax,
          currencyCode: order.currencyCode,
          createdAt: order.createdAt.toISOString(),
          updatedAt: order.updatedAt.toISOString(),
        };
      });
    });
  }

  /**
   * Get customer order history with caching - simplified approach
   */
  @Query()
  async cachedCustomerOrders(
    @Ctx() ctx: RequestContext,
    @Args('customerId') customerId: ID
  ) {
    const cacheKey = `customer-orders:${customerId}`;

    return this.requestContextCache.get(ctx, cacheKey, async () => {
      const customerOrderCache = this.cacheService.createCache({
        getKey: (key: string | number) => `customer-orders:${key}`,
        options: {
          ttl: 240, // 4 minutes (shorter for lists)
          tags: ['order', 'customer-orders', `customer:${customerId}`]
        }
      });

      return customerOrderCache.get(cacheKey, async () => {
        // Simplified: just get basic order data
        const orders = await this.orderService.findAll(ctx, {
          take: 20,
          skip: 0
        });

        return {
          items: orders.items.filter((order: any) => order.customer?.id === customerId).map((order: any) => ({
            id: order.id,
            code: order.code,
            state: order.state,
            total: order.total,
            createdAt: order.createdAt.toISOString(),
          })),
          totalItems: orders.totalItems,
        };
      });
    });
  }
}