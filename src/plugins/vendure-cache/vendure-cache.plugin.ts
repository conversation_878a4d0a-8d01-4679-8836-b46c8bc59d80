import { PluginCommonModule, VendurePlugin } from '@vendure/core';
import { CachedOrderResolver } from './resolvers/cached-order.resolver';
import { CachedProductResolver } from './resolvers/cached-product.resolver';
import { CacheEventHandler } from './handlers/cache-event.handler';

/**
 * Vendure Cache Plugin - Uses Vendure's official caching system
 * This plugin demonstrates the CORRECT way to implement caching in Vendure
 * using CacheService and RequestContextCacheService
 */
@VendurePlugin({
  imports: [PluginCommonModule],
  providers: [
    CacheEventHandler,
  ],
  shopApiExtensions: {
    resolvers: [CachedOrderResolver, CachedProductResolver],
  },
  adminApiExtensions: {
    resolvers: [CachedOrderResolver, CachedProductResolver],
  },
})
export class VendureCachePlugin {
  static init() {
    return VendureCachePlugin;
  }
}