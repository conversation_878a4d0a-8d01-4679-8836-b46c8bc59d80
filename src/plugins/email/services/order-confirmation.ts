import { Injectable, OnModuleInit } from "@nestjs/common";
import {
  JobQueue,
  JobQueueService,
  Order,
  TransactionalConnection,
  SerializedRequestContext,
  RequestContext,
  ID,
} from "@vendure/core";
import nodemailer, { Transporter } from "nodemailer";
import fs from "fs/promises";
import Handlebars from "handlebars";
import { SendEmailCommand, SES } from "@aws-sdk/client-ses";
import mjml2html from "mjml";

const ses = new SES({
  apiVersion: "2010-12-01",
  region: "ap-southeast-1",
  credentials: {
    accessKeyId: process.env.SES_ACCESS_KEY || "",
    secretAccessKey: process.env.SES_SECRET_KEY || "",
  },
});

function formatMoney(amount: number, currencyCode: string, locale: string) {
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency: currencyCode,
  }).format(amount);
}

Handlebars.registerHelper(
  "customFormatMoney",
  function (amount, currencyCode, locale) {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currencyCode,
    }).format(amount);
  }
);

Handlebars.registerHelper("log", function (value) {
  console.log("From Handlebars");
  if (value) {
    console.log(JSON.stringify(value));
  } else {
    // @ts-ignore
    console.log(JSON.stringify(this));
  }
  console.log("From Handlebars end");
  return "";
});

@Injectable()
export class OrderConfirmationService implements OnModuleInit {
  private jobQueue: JobQueue<{ ctx: SerializedRequestContext; orderId: ID }>;
  private transporter: Transporter;

  constructor(
    private jobQueueService: JobQueueService,
    private connection: TransactionalConnection
  ) {
    this.transporter = nodemailer.createTransport({
      host: "pro12.emailserver.vn",
      port: 465,
      secure: true,
      auth: {
        user: "<EMAIL>",
        pass: "Eventista@2024",
      },
      debug: true,
      logger: true,
    });
  }

  async onModuleInit() {
    this.jobQueue = await this.jobQueueService.createQueue({
      name: "custom-order-confirmation",
      process: async (job) => {
        const ctx = RequestContext.deserialize(job.data.ctx);

        const order = await this.connection.getRepository(ctx, Order).findOne({
          where: { id: job.data.orderId },
          relations: [
            "lines",
            "customer",
            "payments",
            "lines.productVariant",
            "lines.productVariant.featuredAsset",
          ],
        });

        if (!order) {
          throw new Error("Order not found");
        }

        const isValidOrder = order.state === "PaymentSettled" || 
          (order.state === "PaymentAuthorized" && 
           order.payments?.some(payment => payment.method === "cod"));

        if (!isValidOrder) {
          throw new Error("Order not paid or not a valid COD order");
        }

        const totalDiscount = order.discounts
          .map((v) => v.amount)
          .reduce((a, b) => a + b, 0);
        const htmlContent = await this.loadTemplate(
          "./static/email/templates/order-confirmation/body.hbs",
          {
            name: "John Doe",
            message: "Welcome to our service!",
            order: order,
            customFormatMoney: formatMoney,
            assetsHost: process.env.ASSETS_HOST,
            totalDiscount: totalDiscount,
            finalPrice: order.total - totalDiscount,
          }
        );
        if (process.env.APP_ENV === "prod") {
          await this.sesSendEmail(
            order.customer?.emailAddress || "",
            "Order Confirmation - Eventista",
            htmlContent
          );
        } else {
          await this.matbaoSendEmail(
            order.customer?.emailAddress || "",
            "Order Confirmation - Eventista",
            htmlContent
          );
        }
        return order;
      },
    });
  }

  sendOrderConfirmation(ctx: RequestContext, orderId: ID) {
    return this.jobQueue.add({ ctx: ctx.serialize(), orderId }, { retries: 2 });
  }

  private async loadTemplate(templatePath: string, data: any) {
    const template = await fs.readFile(templatePath, "utf-8");
    const compiledTemplate = Handlebars.compile(template);
    const { html } = mjml2html(compiledTemplate(data));
    return html;
  }

  private async matbaoSendEmail(email: string, subject: string, html: string) {
    try {
      await this.transporter.sendMail({
        from: "<EMAIL>",
        to: email,
        subject,
        html,
      });
    } catch (error) {
      console.error("Error sending email:", error);
    }
  }

  private async sesSendEmail(email: string, subject: string, html: string) {
    try {
      const command = new SendEmailCommand({
        Destination: {
          /* required */
          CcAddresses: [
            /* more items */
          ],
          ToAddresses: [email],
        },
        Message: {
          /* required */
          Body: {
            /* required */
            Html: {
              Charset: "UTF-8",
              Data: html,
            },
          },
          Subject: {
            Charset: "UTF-8",
            Data: subject,
          },
        },
        Source: "<EMAIL>",
        ReplyToAddresses: [
          /* more items */
        ],
      });
      const response = await ses.send(command);
      console.log(response);
    } catch (error) {
      console.error("Error sending email:", error);
    }
  }
}
