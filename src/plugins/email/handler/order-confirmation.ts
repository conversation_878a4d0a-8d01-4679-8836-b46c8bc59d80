import { EmailEventListener } from "@vendure/email-plugin";
import { OrderStateTransitionEvent } from "@vendure/core";

export const orderConfirmationHandler = new EmailEventListener(
  "order-confirmation"
)
  .on(OrderStateTransitionEvent)
  .filter((event) => {
    console.log(
      1111,
      event.order.state === "PaymentSettled" &&
        !!event.order.customer?.emailAddress
    );
    return (
      event.order.state === "PaymentSettled" &&
      !!event.order.customer?.emailAddress
    );
  })
  .setRecipient((event) => {
    const email = event.order.customer?.emailAddress || "";
    console.log(2222, email);
    return email;
  })
  .setSubject("Eventista Order Confirmation")
  .setFrom("<EMAIL>")
  .setTemplateVars((event) => {
    console.log(3333, event.order);
    return { order: event.order };
  });
