import {
  CancelPaymentResult,
  CancelPaymentErrorResult,
  PaymentMethod<PERSON>andler,
  VendureConfig,
  CreatePaymentResult,
  SettlePaymentResult,
  SettlePaymentErrorResult,
  LanguageCode,
  Order,
} from "@vendure/core";
import hmacSHA256 from "crypto-js/hmac-sha256";
import { format } from "date-fns";

const logo = {
  momo: "https://media-platform.1vote.vn/logo/MoMo_Logo.png",
  zalopay: "https://media-platform.1vote.vn/logo/ZaloPay_Logo.png",
  zalopay_vietqr: "https://media-platform.1vote.vn/logo/ZaloPay_Logo.png",
  zalopay_cc: "https://media-platform.1vote.vn/logo/ZaloPay_Logo.png",
  vnp: "https://media-platform.1vote.vn/logo/Icon-VNPAY-QR.png",
  intcard: "https://media-platform.1vote.vn/logo/visa-master-card.jpg",
  paypal: "https://media-platform.1vote.vn/logo/paypal.png",
  paymentwall: "https://media-platform.1vote.vn/logo/paymentwall-global.png",
};
/**
 * This is a handler which integrates Vendure with an imaginary
 * payment provider, who provide a Node SDK which we use to
 * interact with their APIs.
 */
export const zaloPaymentHandler = new PaymentMethodHandler({
  code: "zalo",
  description: [
    {
      value: "Zalo",
      languageCode: LanguageCode.vi,
    },
  ],
  args: {
    appId: { type: "int" },
    macKey: { type: "string" },
    callbackKey: { type: "string" },
  },

  /** This is called when the `addPaymentToOrder` mutation is executed */
  createPayment: async (
    ctx,
    order,
    amount,
    args,
    metadata,
    method
  ): Promise<CreatePaymentResult> => {
    try {
      console.log(111, method);
      const hasActivePayment = order.payments.some(
        (payment) => !["Declined", "Error"].includes(payment.state)
      );
      if (hasActivePayment) {
        return {
          amount: order.total,
          state: "Declined" as const,
          metadata: {
            errorMessage: "Order already has an active payment",
          },
        };
      }

      const callbackUrl = process.env.ZALO_CALLBACK_URL;
      const { code, total } = order;

      const domain = metadata.public.domain || "faniesta.com";
      const redirectUrl = `https://${domain}/ket-qua-thanh-toan`;
      const transId = `${format(new Date(), "yyMMdd")}_${code}`;
      const description = `Mã giao dịch: ${code}`;
      let preferredPaymentMethod = "";
      switch (method.code) {
        case "zalopay_vietqr":
          preferredPaymentMethod = "vietqr";
          break;
        case "zalopay_cc":
          preferredPaymentMethod = "international_card";
          break;
        case "zalopay":
        default:
          preferredPaymentMethod = "zalopay_wallet";
          break;
      }

      const config = {
        app_id: args.appId,
        app_user: "Eventista",
        app_time: Date.now(),
        expire_duration_seconds: 600,
        amount: total,
        app_trans_id: transId,
        bank_code: "",
        embed_data: JSON.stringify({
          preferred_payment_method: [preferredPaymentMethod],
          redirecturl: redirectUrl + `?orderCode=${code}`,
        }),
        item: JSON.stringify(order.lines.map((line) => line.productVariantId)),
        callback_url: callbackUrl,
        description,
      };

      const rawSignature = [
        config.app_id,
        config.app_trans_id,
        config.app_user,
        config.amount,
        config.app_time,
        config.embed_data,
        config.item,
      ].join("|");
      const mac = hmacSHA256(rawSignature, args.macKey).toString();

      // json object send to ZaloPay endpoint
      const body = {
        ...config,
        mac,
      };
      const result = await fetch("https://sb-openapi.zalopay.vn/v2/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });
      const res = await result.json();

      if (res.return_code === 1) {
        return {
          amount: order.total,
          state: "Authorized" as const,
          metadata: {
            public: {
              orderUrl: res.order_url,
            },
          },
        };
      }
      console.log(44444, res, body);
      return {
        amount: order.total,
        state: "Declined" as const,
      };
    } catch (err) {
      return {
        amount: order.total,
        state: "Declined" as const,
        metadata: {
          errorMessage: err,
        },
      };
    }
  },

  /** This is called when the `settlePayment` mutation is executed */
  settlePayment: async (
    ctx,
    order,
    payment,
    args
  ): Promise<SettlePaymentResult | SettlePaymentErrorResult> => {
    try {
      return { success: true };
    } catch (err) {
      return {
        success: false,
      };
    }
  },

  /** This is called when a payment is cancelled. */
  //   cancelPayment: async (
  //     ctx,
  //     order,
  //     payment,
  //     args
  //   ): Promise<CancelPaymentResult | CancelPaymentErrorResult> => {
  //     try {
  //       const result = await sdk.charges.cancel({
  //         apiKey: args.apiKey,
  //         id: payment.transactionId,
  //       });
  //       return { success: true };
  //     } catch (err) {
  //       return {
  //         success: false,
  //         errorMessage: err.message,
  //       };
  //     }
  //   },
});
