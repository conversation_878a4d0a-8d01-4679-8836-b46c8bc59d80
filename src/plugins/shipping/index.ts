import { PluginCommonModule, VendurePlugin } from "@vendure/core";
import {shopApiExtensions} from './api/shop-extension'
import {ShopShippingResolver} from './api/shop-resolver'
import {ShopShippingService} from './services/shop.service'

@VendurePlugin({
    imports: [PluginCommonModule],
    shopApiExtensions: {
        schema: shopApiExtensions,
        resolvers: [ShopShippingResolver]
    },
    providers: [ShopShippingService]
})

export class ShippingPlugin {}