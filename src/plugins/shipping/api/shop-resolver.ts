import { Query, Resolver } from '@nestjs/graphql';
import { Allow, Ctx, RequestContext, Permission, Transaction } from '@vendure/core';
import { ShopShippingService } from '../services/shop.service';

@Resolver()
export class ShopShippingResolver {
    constructor(private ShopShippingService: ShopShippingService) {}

    @Allow(Permission.Public)
    @Transaction()
    @Query()
    calculateShipping(@Ctx() ctx: RequestContext) {
        return this.ShopShippingService.calculateShipping(ctx);
    }
}
