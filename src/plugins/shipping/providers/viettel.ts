import {
  Order,
  RequestContext,
  ShippingMethod,
  TransactionalConnection,
  StockLocation,
} from "@vendure/core";

export class ViettelProvider {
  constructor(
    private connection: TransactionalConnection,
    private secretKey: any
  ) {}

  public getReceiverAddress(order: Order) {
    const ward = order.shippingAddress.customFields?.ward
      ? `, ${order.shippingAddress.customFields.ward}`
      : "";
    const district = order.shippingAddress.customFields?.district
      ? `, ${order.shippingAddress.customFields.district}`
      : "";
    const province = order.shippingAddress.customFields?.province
      ? `, ${order.shippingAddress.customFields.province}`
      : order.shippingAddress.province
      ? `, ${order.shippingAddress.province}`
      : "";
    return `${order.shippingAddress.streetLine1}${ward}${district}${province}`;
  }

  public async calculateShipping(
    ctx: RequestContext,
    {
      senderAddress,
      receiverAddress,
      productPrice,
    }: { senderAddress: string; receiverAddress: string; productPrice: number }
  ) {
    const token = await this.getToken(ctx);
    const res = await fetch(`${this.secretKey.url}v2/order/getPriceNlp`, {
      method: "POST",
      body: JSON.stringify({
        PRODUCT_WEIGHT: 100,
        PRODUCT_PRICE: productPrice,
        MONEY_COLLECTION: 0,
        ORDER_SERVICE_ADD: "",
        ORDER_SERVICE: "VSL7",
        SENDER_ADDRESS: senderAddress,
        RECEIVER_ADDRESS: receiverAddress,
        PRODUCT_LENGTH: 10,
        PRODUCT_WIDTH: 10,
        PRODUCT_HEIGHT: 10,
        PRODUCT_TYPE: "HH",
        NATIONAL_TYPE: 1,
      }),
      headers: {
        Token: token,
        "Content-Type": "application/json",
      },
    });

    const data = await res.json();
    console.log("data", data);
    if (data.status !== 200) {
      throw new Error("Failed to calculate shipping");
    }
    return Math.ceil(data.data.MONEY_TOTAL / 1000) * 1000;
  }

  public async createOrder(
    ctx: RequestContext,
    { order, stock }: { order: Order; stock: StockLocation }
  ) {
    const token = await this.getToken(ctx);
    const price = order.subTotal;
    const receiverAddress = this.getReceiverAddress(order);
    const senderAddress = stock.customFields.address;
    const listProducts = order.lines.map((line) => ({
      PRODUCT_NAME: line.productVariant.name,
      PRODUCT_QUANTITY: line.quantity,
      PRODUCT_PRICE: line.unitPrice,
      PRODUCT_WEIGHT: 100,
    }));

    const isCodPayment = order.payments?.some(
      (payment) => payment.method === "cod"
    );

    try {
      const body = {
        PRODUCT_WEIGHT: 100,
        PRODUCT_PRICE: price,
        MONEY_COLLECTION: isCodPayment ? price : 0,
        ORDER_SERVICE_ADD: "",
        ORDER_SERVICE: "LCOD",
        SENDER_ADDRESS: senderAddress,
        RECEIVER_ADDRESS: receiverAddress,
        PRODUCT_TYPE: "HH",
        NATIONAL_TYPE: 1,
        ORDER_NUMBER: order.code,
        SENDER_FULLNAME: stock.name,
        SENDER_PHONE: stock.customFields.phone,
        RECEIVER_FULLNAME: order.shippingAddress.fullName,
        RECEIVER_PHONE: order.shippingAddress.phoneNumber,
        PRODUCT_NAME: "",
        PRODUCT_DESCRIPTION: "",
        PRODUCT_QUANTITY: order.lines.reduce(
          (acc, line) => acc + line.quantity,
          0
        ),
        ORDER_PAYMENT: isCodPayment ? 2 : 1,
        ORDER_NOTE: "",
        EXTRA_MONEY: 0,
        CHECK_UNIQUE: true,
        PRODUCT_DETAIL: listProducts,
      };

      console.log("body", body);

      const res = await fetch(`${this.secretKey.url}v2/order/createOrder`, {
        method: "POST",
        body: JSON.stringify(body),
        headers: {
          Token: token,
          "Content-Type": "application/json",
        },
      });
      const data = await res.json();
      console.log("data", data);

      if (data.status !== 200) {
        throw new Error(data.message);
      }
      return data.data;
    } catch (error) {
      throw new Error(`Failed to create order: ${error}`);
    }
  }

  private async getToken(ctx: RequestContext) {
    const shippingMethods = await this.connection
      .getRepository(ctx, ShippingMethod)
      .find({
        where: {
          code: "viettel-post",
        },
      });

    if (shippingMethods.length === 0) {
      throw new Error("Viettel shipping method not found");
    }
    const shippingMethod = shippingMethods[0];

    if (shippingMethod.customFields?.token) {
      return shippingMethod.customFields.token;
    }

    const response = await fetch(`${this.secretKey.url}v2/user/login`, {
      method: "POST",
      body: JSON.stringify({
        USERNAME: this.secretKey.username,
        PASSWORD: this.secretKey.password,
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });
    const data = await response.json();

    if (data.status !== 200) {
      throw new Error("Failed to get token");
    }

    const longTokenResponse = await fetch(
      `${this.secretKey.url}v2/user/ownerconnect`,
      {
        method: "POST",
        body: JSON.stringify({
          USERNAME: this.secretKey.username,
          PASSWORD: this.secretKey.password,
        }),
        headers: {
          Token: data.data.token,
          "Content-Type": "application/json",
        },
      }
    );
    const longTokenData = await longTokenResponse.json();
    if (longTokenData.status !== 200) {
      throw new Error("Failed to get long token");
    }
    await this.connection
      .getRepository(ctx, ShippingMethod)
      .update(shippingMethod.id, {
        // translations: [],
        customFields: {
          token: longTokenData.data.token,
        },
      });

    return longTokenData.data.token;
  }
}
