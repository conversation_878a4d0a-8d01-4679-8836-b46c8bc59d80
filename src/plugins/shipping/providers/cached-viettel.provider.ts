import { Injectable } from '@nestjs/common';
import { RequestContext, Order, StockLocation } from "@vendure/core";
import { ViettelProvider } from './viettel';
import { CacheService } from '../../rest-plugin/services/cache.service';
import crypto from 'crypto';

interface ShippingCalculationParams {
  senderAddress: string;
  receiverAddress: string;
  productPrice: number;
}

@Injectable()
export class CachedViettelProvider {
  constructor(
    private viettelProvider: ViettelProvider,
    private cacheService: CacheService
  ) {}

  /**
   * Calculate shipping with caching
   * Cache key is based on sender/receiver addresses and product price
   */
  async calculateShipping(
    ctx: RequestContext,
    params: ShippingCalculationParams
  ): Promise<number> {
    const cacheKey = this.generateShippingCacheKey(params);

    return this.cacheService.getOrSet(
      cacheKey,
      () => this.viettelProvider.calculateShipping(ctx, params),
      { ttl: 1800 } // 30 minutes cache for shipping calculations
    );
  }

  /**
   * Create order - no caching for order creation as it should be unique
   */
  async createOrder(
    ctx: RequestContext,
    { order, stock }: { order: Order; stock: StockLocation }
  ): Promise<any> {
    try {
      return await this.viettelProvider.createOrder(ctx, { order, stock });
    } catch (error: any) {
      // If token might be expired, clear the cache
      if (error?.message?.includes('token') || error?.message?.includes('unauthorized')) {
        await this.invalidateTokenCache(Number(ctx.channel.id));
      }
      throw error;
    }
  }

  /**
   * Get receiver address (no caching needed as it's just string manipulation)
   */
  getReceiverAddress(order: Order): string {
    return this.viettelProvider.getReceiverAddress(order);
  }

  /**
   * Invalidate shipping cache for specific addresses
   */
  async invalidateShippingCache(senderAddress?: string, receiverAddress?: string): Promise<void> {
    if (senderAddress && receiverAddress) {
      // Invalidate specific cache entry
      const params = { senderAddress, receiverAddress, productPrice: 0 };
      const cacheKey = this.generateShippingCacheKey(params);
      await this.cacheService.del(cacheKey);
    } else {
      // Invalidate all shipping calculations
      await this.cacheService.delPattern('viettel:shipping:*');
    }
  }

  /**
   * Invalidate authentication token cache
   */
  async invalidateTokenCache(channelId?: number): Promise<void> {
    if (channelId) {
      await this.cacheService.del(`viettel:token:${channelId}`);
    } else {
      await this.cacheService.delPattern('viettel:token:*');
    }
  }

  private generateShippingCacheKey(params: ShippingCalculationParams): string {
    // Create a hash of the parameters to ensure consistent cache keys
    const paramString = `${params.senderAddress}|${params.receiverAddress}|${params.productPrice}`;
    const hash = crypto.createHash('md5').update(paramString).digest('hex');
    return `viettel:shipping:${hash}`;
  }
}