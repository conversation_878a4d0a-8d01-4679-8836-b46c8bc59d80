import { Injectable } from "@nestjs/common";
import {
  RequestContext,
  TransactionalConnection,
  ShippingMethodService,
  OrderService,
  StockLocationService,
} from "@vendure/core";
import { ViettelProvider } from "../providers/viettel";
import { getSecretKey } from "../../../utils/getSecretKey";

@Injectable()
export class ShopShippingService {
  private viettelProvider: ViettelProvider;
  private secretKey: any;
  constructor(
    private connection: TransactionalConnection,
    private orderService: OrderService,
    private shippingMethodService: ShippingMethodService,
    private stockLocationService: StockLocationService
  ) {
    this.init();
  }
  private async init() {
    await this.getSecretKey();
    this.viettelProvider = new ViettelProvider(this.connection, this.secretKey);
  }
  private async getSecretKey() {
    const secretKey = await getSecretKey();
    this.secretKey = secretKey;
  }
  async calculateShipping(ctx: RequestContext) {
    const order = await this.orderService.findOne(
      ctx,
      ctx.session?.activeOrderId ?? 0
    );

    if (!order) {
      return 0;
    }

    const stocks = await this.stockLocationService.findAll(ctx);

    if (stocks.totalItems === 0) {
      return 0;
    }
    const stock = stocks.items[0];
    const address = this.viettelProvider.getReceiverAddress(order);
    const price = await this.viettelProvider.calculateShipping(ctx, {
      senderAddress: stock.customFields.address ?? "",
      receiverAddress: address,
      productPrice: order.subTotal,
    });
    return price;
  }
}
