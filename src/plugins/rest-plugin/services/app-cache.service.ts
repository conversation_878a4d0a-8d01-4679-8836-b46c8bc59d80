import { Injectable, OnModuleInit } from '@nestjs/common';
import {
  RequestContext,
  TransactionalConnection,
  Channel,
  ShippingMethod,
  PaymentMethod,
  TaxRate,
  Zone
} from '@vendure/core';
import { CacheService } from './cache.service';

@Injectable()
export class AppCacheService implements OnModuleInit {
  constructor(
    private connection: TransactionalConnection,
    private cacheService: CacheService
  ) {}

  async onModuleInit() {
    // Pre-warm critical caches on startup
    await this.preWarmCaches();
  }

  /**
   * Get all channels with caching
   */
  async getChannels(ctx: RequestContext): Promise<Channel[]> {
    const cacheKey = 'config:channels';

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        const result = await this.connection.getRepository(ctx, Channel).find({
          where: { code: { $ne: '__default_channel__' } as any }
        });
        return result;
      },
      { ttl: 3600 } // 1 hour cache for channels
    );
  }

  /**
   * Get shipping methods with caching
   */
  async getShippingMethods(ctx: RequestContext): Promise<ShippingMethod[]> {
    const cacheKey = `config:shipping-methods:${ctx.channel.id}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, ShippingMethod).find({
          where: { enabled: true },
          relations: ['translations']
        });
      },
      { ttl: 1800 } // 30 minutes cache
    );
  }

  /**
   * Get payment methods with caching
   */
  async getPaymentMethods(ctx: RequestContext): Promise<PaymentMethod[]> {
    const cacheKey = `config:payment-methods:${ctx.channel.id}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, PaymentMethod).find({
          where: { enabled: true },
          relations: ['translations']
        });
      },
      { ttl: 1800 } // 30 minutes cache
    );
  }

  /**
   * Get tax rates with caching
   */
  async getTaxRates(ctx: RequestContext): Promise<TaxRate[]> {
    const cacheKey = `config:tax-rates:${ctx.channel.id}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, TaxRate).find({
          where: { enabled: true }
        });
      },
      { ttl: 3600 } // 1 hour cache
    );
  }

  /**
   * Get zones with caching
   */
  async getZones(ctx: RequestContext): Promise<Zone[]> {
    const cacheKey = 'config:zones';

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, Zone).find({
          relations: ['members']
        });
      },
      { ttl: 3600 } // 1 hour cache
    );
  }

  /**
   * Cache business rules and configurations
   */
  async getBusinessConfig(key: string, defaultValue: any = null): Promise<any> {
    const cacheKey = `business:config:${key}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        // This could be from database, environment, or external config service
        const config = {
          'max-order-items': 50,
          'min-order-amount': 10000, // 10,000 VND
          'free-shipping-threshold': 500000, // 500,000 VND
          'cod-fee': 15000, // 15,000 VND
          'return-window-days': 7,
          'loyalty-points-rate': 0.01, // 1% of order value
          'featured-products-count': 12,
          'popular-brands': ['Nike', 'Adidas', 'Samsung', 'Apple'],
          'supported-payment-methods': ['zalopay', 'zalopay_vietqr', 'cod'],
          'shipping-zones': ['hanoi', 'hcm', 'danang', 'other']
        };

        return config[key] || defaultValue;
      },
      { ttl: 7200 } // 2 hours cache for business config
    );
  }

  /**
   * Cache frequently accessed calculations
   */
  async getCachedCalculation(
    calculationType: string,
    params: any,
    calculationFn: () => Promise<any>
  ): Promise<any> {
    const paramHash = Buffer.from(JSON.stringify(params)).toString('base64').slice(0, 16);
    const cacheKey = `calc:${calculationType}:${paramHash}`;

    return this.cacheService.getOrSet(
      cacheKey,
      calculationFn,
      { ttl: 600 } // 10 minutes cache for calculations
    );
  }

  /**
   * Cache popular/trending data
   */
  async getPopularProducts(ctx: RequestContext, limit: number = 10): Promise<any[]> {
    const cacheKey = `popular:products:${ctx.channel.id}:${limit}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        // This would typically come from analytics or order data
        // For now, return mock data structure
        return [];
      },
      { ttl: 1800 } // 30 minutes cache
    );
  }

  /**
   * Cache search suggestions
   */
  async getSearchSuggestions(query: string, ctx: RequestContext): Promise<string[]> {
    const cacheKey = `search:suggestions:${query.toLowerCase()}:${ctx.channel.id}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        // This would typically come from search analytics or product names
        // For now, return empty array
        return [];
      },
      { ttl: 3600 } // 1 hour cache
    );
  }

  /**
   * Invalidate configuration caches
   */
  async invalidateConfigCache(type?: string): Promise<void> {
    const patterns = type ? [`config:${type}:*`] : ['config:*'];

    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }

  /**
   * Invalidate business configuration cache
   */
  async invalidateBusinessConfig(key?: string): Promise<void> {
    if (key) {
      await this.cacheService.del(`business:config:${key}`);
    } else {
      await this.cacheService.delPattern('business:config:*');
    }
  }

  /**
   * Pre-warm critical caches on startup
   */
  private async preWarmCaches(): Promise<void> {
    try {
      // Create a basic request context for pre-warming
      const ctx = new RequestContext({
        apiType: 'admin',
        isAuthorized: true,
        authorizedAsOwnerOnly: false,
        channel: { id: '1' } as any, // Default channel
      });

      // Pre-warm critical configuration caches
      await Promise.all([
        this.getBusinessConfig('max-order-items'),
        this.getBusinessConfig('min-order-amount'),
        this.getBusinessConfig('free-shipping-threshold'),
        this.getBusinessConfig('supported-payment-methods'),
        // Add more critical configs as needed
      ]);

      console.log('✅ Application caches pre-warmed successfully');
    } catch (error) {
      console.warn('⚠️ Failed to pre-warm application caches:', error);
    }
  }
}