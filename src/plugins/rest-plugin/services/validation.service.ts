import { Injectable, BadRequestException } from "@nestjs/common";

@Injectable()
export class ValidationService {
  validateRequired(value: any, fieldName: string): void {
    if (!value) {
      throw new BadRequestException(`${fieldName} is required`);
    }
  }

  validateDateRange(from: string, to: string): void {
    this.validateRequired(from, "from date");
    this.validateRequired(to, "to date");

    const fromDate = new Date(from);
    const toDate = new Date(to);

    if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
      throw new BadRequestException("Invalid date format");
    }

    if (fromDate > toDate) {
      throw new BadRequestException("From date must be before to date");
    }
  }
}
