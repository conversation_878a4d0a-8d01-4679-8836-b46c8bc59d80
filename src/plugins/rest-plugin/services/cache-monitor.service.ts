import { Injectable, OnModuleInit } from '@nestjs/common';
import { CacheService } from './cache.service';

interface CacheMetrics {
  hits: number;
  misses: number;
  hitRate: number;
  totalKeys: number;
  memoryUsage: number;
  redisConnected: boolean;
  topKeys: Array<{ key: string; hits: number }>;
  slowQueries: Array<{ key: string; duration: number; timestamp: Date }>;
}

interface CacheAlert {
  type: 'LOW_HIT_RATE' | 'HIGH_MEMORY' | 'REDIS_DOWN' | 'SLOW_QUERY';
  message: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH';
  timestamp: Date;
  metadata?: any;
}

@Injectable()
export class CacheMonitorService implements OnModuleInit {
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalKeys: 0,
    memoryUsage: 0,
    redisConnected: false,
    topKeys: [],
    slowQueries: []
  };

  private alerts: CacheAlert[] = [];
  private keyHitCounts = new Map<string, number>();
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(private cacheService: CacheService) {}

  async onModuleInit() {
    // Start monitoring
    this.startMonitoring();
  }

  /**
   * Get current cache metrics
   */
  async getMetrics(): Promise<CacheMetrics> {
    const stats = this.cacheService.getStats();

    this.metrics = {
      ...this.metrics,
      hits: stats.hits,
      misses: stats.misses,
      hitRate: stats.hitRate,
      totalKeys: stats.keys,
      redisConnected: this.cacheService.isRedisAvailable(),
      topKeys: this.getTopKeys(),
      slowQueries: this.getSlowQueries()
    };

    return this.metrics;
  }

  /**
   * Get cache alerts
   */
  getAlerts(severity?: 'LOW' | 'MEDIUM' | 'HIGH'): CacheAlert[] {
    if (severity) {
      return this.alerts.filter(alert => alert.severity === severity);
    }
    return this.alerts;
  }

  /**
   * Record cache operation for monitoring
   */
  recordCacheOperation(key: string, operation: 'hit' | 'miss', duration: number): void {
    // Update key hit counts
    if (operation === 'hit') {
      this.keyHitCounts.set(key, (this.keyHitCounts.get(key) || 0) + 1);
    }

    // Record slow queries
    if (duration > 100) { // More than 100ms is considered slow
      this.metrics.slowQueries.push({
        key,
        duration,
        timestamp: new Date()
      });

      // Keep only last 100 slow queries
      if (this.metrics.slowQueries.length > 100) {
        this.metrics.slowQueries = this.metrics.slowQueries.slice(-100);
      }

      // Create alert for very slow queries
      if (duration > 1000) { // More than 1 second
        this.createAlert('SLOW_QUERY', `Slow cache query detected: ${key} (${duration}ms)`, 'HIGH', {
          key,
          duration
        });
      }
    }
  }

  /**
   * Generate cache performance report
   */
  async generateReport(): Promise<{
    summary: CacheMetrics;
    recommendations: string[];
    alerts: CacheAlert[];
  }> {
    const metrics = await this.getMetrics();
    const recommendations = this.generateRecommendations(metrics);
    const alerts = this.getAlerts();

    return {
      summary: metrics,
      recommendations,
      alerts
    };
  }

  /**
   * Clear old alerts
   */
  clearOldAlerts(olderThanHours: number = 24): void {
    const cutoff = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoff);
  }

  /**
   * Export metrics for external monitoring systems
   */
  async exportMetrics(): Promise<{
    timestamp: Date;
    metrics: CacheMetrics;
    alerts: CacheAlert[];
  }> {
    return {
      timestamp: new Date(),
      metrics: await this.getMetrics(),
      alerts: this.getAlerts()
    };
  }

  private startMonitoring(): void {
    // Monitor every 30 seconds
    this.monitoringInterval = setInterval(async () => {
      await this.checkHealth();
    }, 30000);

    console.log('✅ Cache monitoring started');
  }

  private async checkHealth(): Promise<void> {
    const metrics = await this.getMetrics();

    // Check hit rate
    if (metrics.hitRate < 70 && metrics.hits + metrics.misses > 100) {
      this.createAlert(
        'LOW_HIT_RATE',
        `Cache hit rate is low: ${metrics.hitRate.toFixed(1)}%`,
        'MEDIUM',
        { hitRate: metrics.hitRate }
      );
    }

    // Check Redis connection
    if (!metrics.redisConnected) {
      this.createAlert(
        'REDIS_DOWN',
        'Redis connection is down, using in-memory cache only',
        'HIGH'
      );
    }

    // Clean up old alerts
    this.clearOldAlerts();
  }

  private createAlert(
    type: CacheAlert['type'],
    message: string,
    severity: CacheAlert['severity'],
    metadata?: any
  ): void {
    // Avoid duplicate alerts
    const existingAlert = this.alerts.find(
      alert => alert.type === type && alert.message === message
    );

    if (!existingAlert) {
      this.alerts.push({
        type,
        message,
        severity,
        timestamp: new Date(),
        metadata
      });

      // Keep only last 1000 alerts
      if (this.alerts.length > 1000) {
        this.alerts = this.alerts.slice(-1000);
      }

      console.warn(`Cache Alert [${severity}]: ${message}`);
    }
  }

  private getTopKeys(): Array<{ key: string; hits: number }> {
    return Array.from(this.keyHitCounts.entries())
      .map(([key, hits]) => ({ key, hits }))
      .sort((a, b) => b.hits - a.hits)
      .slice(0, 20); // Top 20 keys
  }

  private getSlowQueries(): Array<{ key: string; duration: number; timestamp: Date }> {
    return this.metrics.slowQueries
      .sort((a, b) => b.duration - a.duration)
      .slice(0, 10); // Top 10 slowest queries
  }

  private generateRecommendations(metrics: CacheMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.hitRate < 70) {
      recommendations.push('Consider increasing cache TTL for frequently accessed data');
      recommendations.push('Review cache key patterns to ensure optimal caching strategy');
    }

    if (metrics.slowQueries.length > 10) {
      recommendations.push('Optimize slow cache queries or increase cache warming');
    }

    if (!metrics.redisConnected) {
      recommendations.push('Restore Redis connection for better performance and persistence');
    }

    if (metrics.totalKeys > 10000) {
      recommendations.push('Consider implementing cache key cleanup or shorter TTL for some keys');
    }

    if (recommendations.length === 0) {
      recommendations.push('Cache performance is optimal');
    }

    return recommendations;
  }
}