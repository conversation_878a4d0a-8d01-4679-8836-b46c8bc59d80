import { Injectable, OnModuleInit } from '@nestjs/common';
import { EventBus, VendureEvent } from '@vendure/core';
import { CacheService } from './cache.service';
import { CachedOrderService } from './cached-order.service';
import { CachedProductService } from './cached-product.service';
import { AppCacheService } from './app-cache.service';
import { CDNService } from './cdn.service';

interface InvalidationRule {
  eventType: string;
  patterns: string[];
  customHandler?: (event: any) => Promise<void>;
}

@Injectable()
export class CacheInvalidationService implements OnModuleInit {
  private invalidationRules: InvalidationRule[] = [];

  constructor(
    private eventBus: EventBus,
    private cacheService: CacheService,
    private cachedOrderService: CachedOrderService,
    private cachedProductService: CachedProductService,
    private appCacheService: AppCacheService,
    private cdnService: CDNService
  ) {
    this.setupInvalidationRules();
  }

  async onModuleInit() {
    // Subscribe to Vendure events for automatic cache invalidation
    this.subscribeToEvents();
  }

  /**
   * Manual cache invalidation by entity type and ID
   */
  async invalidateEntity(entityType: string, entityId: number, additionalData?: any): Promise<void> {
    switch (entityType.toLowerCase()) {
      case 'order':
        await this.invalidateOrderCache(entityId, additionalData);
        break;
      case 'product':
        await this.invalidateProductCache(entityId, additionalData);
        break;
      case 'customer':
        await this.invalidateCustomerCache(entityId);
        break;
      case 'channel':
        await this.invalidateChannelCache(entityId);
        break;
      case 'asset':
        await this.invalidateAssetCache(additionalData?.assetPath);
        break;
      default:
        console.warn(`Unknown entity type for cache invalidation: ${entityType}`);
    }
  }

  /**
   * Invalidate order-related caches
   */
  async invalidateOrderCache(orderId: number, additionalData?: any): Promise<void> {
    const { customerId, orderCode, channelId } = additionalData || {};

    // Use the cached order service invalidation
    await this.cachedOrderService.invalidateOrderCache(orderId, customerId, orderCode);

    // Invalidate related caches
    const patterns = [
      `orders:*:${channelId || '*'}:*`,
      'popular:*',
      'analytics:*'
    ];

    await this.invalidatePatterns(patterns);
  }

  /**
   * Invalidate product-related caches
   */
  async invalidateProductCache(productId: number, additionalData?: any): Promise<void> {
    const { brand, categoryId, channelId } = additionalData || {};

    // Use the cached product service invalidation
    await this.cachedProductService.invalidateProductCache(productId, brand);

    // Invalidate related caches
    const patterns = [
      'products:*',
      'search:*',
      'popular:products:*',
      `category:${categoryId}:*`,
      'featured:*'
    ];

    await this.invalidatePatterns(patterns);
  }

  /**
   * Invalidate customer-related caches
   */
  async invalidateCustomerCache(customerId: number): Promise<void> {
    const patterns = [
      `customer:${customerId}:*`,
      `session:*${customerId}*`,
      `auth:*${customerId}*`
    ];

    await this.invalidatePatterns(patterns);
  }

  /**
   * Invalidate channel-related caches
   */
  async invalidateChannelCache(channelId: number): Promise<void> {
    const patterns = [
      `*:${channelId}:*`,
      `*:${channelId}`,
      'config:channels'
    ];

    await this.invalidatePatterns(patterns);
  }

  /**
   * Invalidate asset-related caches
   */
  async invalidateAssetCache(assetPath?: string): Promise<void> {
    await this.cdnService.invalidateAssetCache(assetPath);
  }

  /**
   * Invalidate configuration caches
   */
  async invalidateConfigCache(configType?: string): Promise<void> {
    await this.appCacheService.invalidateConfigCache(configType);
  }

  /**
   * Bulk invalidation for multiple entities
   */
  async bulkInvalidate(invalidations: Array<{
    entityType: string;
    entityId: number;
    additionalData?: any;
  }>): Promise<void> {
    const promises = invalidations.map(({ entityType, entityId, additionalData }) =>
      this.invalidateEntity(entityType, entityId, additionalData)
    );

    await Promise.all(promises);
  }

  /**
   * Time-based cache invalidation (for scheduled cleanup)
   */
  async scheduleInvalidation(patterns: string[], delayMs: number): Promise<void> {
    setTimeout(async () => {
      await this.invalidatePatterns(patterns);
    }, delayMs);
  }

  /**
   * Conditional invalidation based on business rules
   */
  async conditionalInvalidate(condition: () => boolean, patterns: string[]): Promise<void> {
    if (condition()) {
      await this.invalidatePatterns(patterns);
    }
  }

  /**
   * Get cache invalidation statistics
   */
  async getInvalidationStats(): Promise<{
    totalInvalidations: number;
    patternInvalidations: number;
    lastInvalidation: Date | null;
  }> {
    // This would typically be stored in a separate metrics system
    return {
      totalInvalidations: 0,
      patternInvalidations: 0,
      lastInvalidation: null
    };
  }

  private async invalidatePatterns(patterns: string[]): Promise<void> {
    const promises = patterns.map(pattern => this.cacheService.delPattern(pattern));
    await Promise.all(promises);
  }

  private setupInvalidationRules(): void {
    this.invalidationRules = [
      {
        eventType: 'OrderStateTransitionEvent',
        patterns: ['orders:*', 'customer:*:orders:*', 'analytics:*'],
        customHandler: async (event: any) => {
          await this.invalidateOrderCache(event.order.id, {
            customerId: event.order.customerId,
            orderCode: event.order.code,
            channelId: event.order.channelId
          });
        }
      },
      {
        eventType: 'ProductEvent',
        patterns: ['products:*', 'search:*', 'popular:*'],
        customHandler: async (event: any) => {
          await this.invalidateProductCache(event.product.id, {
            brand: event.product.customFields?.brand,
            channelId: event.ctx.channelId
          });
        }
      },
      {
        eventType: 'AssetEvent',
        patterns: ['asset:*'],
        customHandler: async (event: any) => {
          await this.invalidateAssetCache(event.asset?.source);
        }
      },
      {
        eventType: 'CustomerEvent',
        patterns: ['customer:*', 'session:*'],
        customHandler: async (event: any) => {
          await this.invalidateCustomerCache(event.customer.id);
        }
      }
    ];
  }

  private subscribeToEvents(): void {
    // Subscribe to Vendure events for automatic invalidation
    this.invalidationRules.forEach(rule => {
      this.eventBus.ofType(rule.eventType as any).subscribe(async (event) => {
        try {
          // Apply pattern-based invalidation
          await this.invalidatePatterns(rule.patterns);

          // Apply custom handler if provided
          if (rule.customHandler) {
            await rule.customHandler(event);
          }

          console.log(`Cache invalidated for event: ${rule.eventType}`);
        } catch (error) {
          console.error(`Failed to invalidate cache for event ${rule.eventType}:`, error);
        }
      });
    });
  }
}