import { Injectable } from '@nestjs/common';
import { CacheService } from './cache.service';
import crypto from 'crypto';

interface AuthResponse {
  success: boolean;
  user?: any;
  token?: string;
  errorCode?: number;
  message?: string;
}

interface ExternalAuthRequest {
  email?: string;
  phone?: string;
  password: string;
  platform?: string;
}

@Injectable()
export class CachedAuthService {
  constructor(private cacheService: CacheService) {}

  /**
   * Cache external authentication responses (with short TTL for security)
   */
  async cacheAuthResponse(
    provider: string,
    request: ExternalAuthRequest,
    response: AuthResponse
  ): Promise<void> {
    if (response.success && response.user) {
      const cacheKey = this.generateAuthCacheKey(provider, request);

      // Only cache successful responses and for a very short time
      await this.cacheService.set(
        cacheKey,
        {
          user: response.user,
          timestamp: Date.now()
        },
        { ttl: 300 } // 5 minutes only for security
      );
    }
  }

  /**
   * Get cached authentication response
   */
  async getCachedAuthResponse(
    provider: string,
    request: ExternalAuthRequest
  ): Promise<any | null> {
    const cacheKey = this.generateAuthCacheKey(provider, request);
    const cached = await this.cacheService.get<any>(cacheKey);

    if (cached && cached.timestamp) {
      // Check if cache is still valid (additional security check)
      const age = Date.now() - cached.timestamp;
      if (age < 300000) { // 5 minutes
        return cached.user;
      } else {
        // Remove expired cache
        await this.cacheService.del(cacheKey);
      }
    }

    return null;
  }

  /**
   * Cache user session data
   */
  async cacheUserSession(userId: string, sessionData: any): Promise<void> {
    const cacheKey = `session:${userId}`;
    await this.cacheService.set(
      cacheKey,
      sessionData,
      { ttl: 1800 } // 30 minutes for session data
    );
  }

  /**
   * Get cached user session
   */
  async getCachedUserSession(userId: string): Promise<any | null> {
    const cacheKey = `session:${userId}`;
    return this.cacheService.get(cacheKey);
  }

  /**
   * Cache external user profile data
   */
  async cacheUserProfile(provider: string, userId: string, profile: any): Promise<void> {
    const cacheKey = `profile:${provider}:${userId}`;
    await this.cacheService.set(
      cacheKey,
      profile,
      { ttl: 3600 } // 1 hour for profile data
    );
  }

  /**
   * Get cached user profile
   */
  async getCachedUserProfile(provider: string, userId: string): Promise<any | null> {
    const cacheKey = `profile:${provider}:${userId}`;
    return this.cacheService.get(cacheKey);
  }

  /**
   * Invalidate authentication cache for a user
   */
  async invalidateUserAuth(provider: string, identifier: string): Promise<void> {
    const patterns = [
      `auth:${provider}:*${identifier}*`,
      `session:*${identifier}*`,
      `profile:${provider}:*${identifier}*`
    ];

    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }

  /**
   * Invalidate all authentication cache for a provider
   */
  async invalidateProviderAuth(provider: string): Promise<void> {
    const patterns = [
      `auth:${provider}:*`,
      `profile:${provider}:*`
    ];

    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }

  private generateAuthCacheKey(provider: string, request: ExternalAuthRequest): string {
    const identifier = request.email || request.phone || '';
    const hash = crypto
      .createHash('sha256')
      .update(`${identifier}:${request.password}`)
      .digest('hex')
      .substring(0, 16);

    return `auth:${provider}:${hash}`;
  }
}