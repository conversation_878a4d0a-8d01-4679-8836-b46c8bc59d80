import { Injectable } from '@nestjs/common';
import { CacheService } from './cache.service';
import path from 'path';
import fs from 'fs/promises';

interface AssetInfo {
  url: string;
  size: number;
  mimeType: string;
  lastModified: Date;
  etag: string;
}

@Injectable()
export class CDNService {
  private readonly cdnBaseUrl: string;
  private readonly localAssetPath: string;

  constructor(private cacheService: CacheService) {
    this.cdnBaseUrl = process.env.CDN_BASE_URL || process.env.ASSETS_HOST || '';
    this.localAssetPath = path.join(process.cwd(), 'static/assets');
  }

  /**
   * Get optimized asset URL with CDN support
   */
  async getAssetUrl(assetPath: string, options?: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png';
  }): Promise<string> {
    const cacheKey = `asset:url:${assetPath}:${JSON.stringify(options || {})}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        let url = this.cdnBaseUrl + assetPath;

        // Add image optimization parameters if supported by CDN
        if (options && this.supportsCDNOptimization()) {
          const params = new URLSearchParams();
          if (options.width) params.set('w', options.width.toString());
          if (options.height) params.set('h', options.height.toString());
          if (options.quality) params.set('q', options.quality.toString());
          if (options.format) params.set('f', options.format);

          if (params.toString()) {
            url += '?' + params.toString();
          }
        }

        return url;
      },
      { ttl: 3600 } // 1 hour cache for asset URLs
    );
  }

  /**
   * Get asset metadata with caching
   */
  async getAssetInfo(assetPath: string): Promise<AssetInfo | null> {
    const cacheKey = `asset:info:${assetPath}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        try {
          const fullPath = path.join(this.localAssetPath, assetPath);
          const stats = await fs.stat(fullPath);

          return {
            url: await this.getAssetUrl(assetPath),
            size: stats.size,
            mimeType: this.getMimeType(assetPath),
            lastModified: stats.mtime,
            etag: `"${stats.mtime.getTime()}-${stats.size}"`
          };
        } catch (error) {
          return null;
        }
      },
      { ttl: 1800 } // 30 minutes cache for asset info
    );
  }

  /**
   * Generate responsive image URLs
   */
  async getResponsiveImageUrls(assetPath: string): Promise<{
    small: string;
    medium: string;
    large: string;
    original: string;
  }> {
    const cacheKey = `asset:responsive:${assetPath}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => ({
        small: await this.getAssetUrl(assetPath, { width: 400, quality: 80, format: 'webp' }),
        medium: await this.getAssetUrl(assetPath, { width: 800, quality: 85, format: 'webp' }),
        large: await this.getAssetUrl(assetPath, { width: 1200, quality: 90, format: 'webp' }),
        original: await this.getAssetUrl(assetPath)
      }),
      { ttl: 3600 } // 1 hour cache
    );
  }

  /**
   * Cache email template assets
   */
  async cacheEmailAssets(templateName: string): Promise<void> {
    const templatePath = path.join(process.cwd(), 'static/email/templates', templateName);

    try {
      const files = await fs.readdir(templatePath, { recursive: true });
      const assetFiles = files.filter(file =>
        typeof file === 'string' && /\.(png|jpg|jpeg|gif|svg)$/i.test(file)
      );

      // Pre-cache email assets
      for (const assetFile of assetFiles) {
        const assetPath = path.relative(this.localAssetPath, path.join(templatePath, assetFile as string));
        await this.getAssetInfo(assetPath);
      }
    } catch (error) {
      console.warn(`Failed to cache email assets for template ${templateName}:`, error);
    }
  }

  /**
   * Invalidate asset cache
   */
  async invalidateAssetCache(assetPath?: string): Promise<void> {
    if (assetPath) {
      const patterns = [
        `asset:url:${assetPath}:*`,
        `asset:info:${assetPath}`,
        `asset:responsive:${assetPath}`
      ];

      for (const pattern of patterns) {
        await this.cacheService.delPattern(pattern);
      }
    } else {
      // Clear all asset caches
      await this.cacheService.delPattern('asset:*');
    }
  }

  /**
   * Set cache headers for static assets
   */
  setCacheHeaders(res: any, assetPath: string): void {
    const isImmutable = /\.(js|css|woff2?|ttf|eot)$/.test(assetPath);
    const isImage = /\.(png|jpg|jpeg|gif|svg|webp)$/.test(assetPath);

    if (isImmutable) {
      // Cache immutable assets for 1 year
      res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
    } else if (isImage) {
      // Cache images for 1 week
      res.setHeader('Cache-Control', 'public, max-age=604800');
    } else {
      // Cache other assets for 1 day
      res.setHeader('Cache-Control', 'public, max-age=86400');
    }

    // Add ETag for better caching
    res.setHeader('ETag', `"${Date.now()}"`);
  }

  private supportsCDNOptimization(): boolean {
    // Check if CDN supports image optimization (e.g., Cloudflare, AWS CloudFront)
    return this.cdnBaseUrl.includes('cloudflare') ||
           this.cdnBaseUrl.includes('cloudfront') ||
           this.cdnBaseUrl.includes('imgix');
  }

  private getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.webp': 'image/webp',
      '.css': 'text/css',
      '.js': 'application/javascript',
      '.json': 'application/json',
      '.pdf': 'application/pdf'
    };

    return mimeTypes[ext] || 'application/octet-stream';
  }
}