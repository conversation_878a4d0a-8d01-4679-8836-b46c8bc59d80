import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';
import NodeCache from 'node-cache';

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  useRedis?: boolean; // Whether to use Redis or in-memory cache
}

export interface CacheStats {
  hits: number;
  misses: number;
  keys: number;
  hitRate: number;
}

@Injectable()
export class CacheService implements OnModuleInit, OnModuleDestroy {
  private redis: Redis;
  private memoryCache: NodeCache;
  private stats = {
    hits: 0,
    misses: 0,
  };

  constructor() {
    // Initialize in-memory cache for fallback
    this.memoryCache = new NodeCache({
      stdTTL: 300, // 5 minutes default
      checkperiod: 60, // Check for expired keys every minute
      useClones: false, // Better performance
    });
  }

  async onModuleInit() {
    try {
      // Initialize Redis connection
      this.redis = new Redis({
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        // Connection pool settings
        family: 4,
        keepAlive: true,
        // Cluster support for production
        ...(process.env.REDIS_CLUSTER === 'true' && {
          enableOfflineQueue: false,
        }),
      });

      await this.redis.connect();
      console.log('✅ Redis cache connected successfully');
    } catch (error: any) {
      console.warn('⚠️ Redis connection failed, using in-memory cache only:', error?.message || error);
    }
  }

  async onModuleDestroy() {
    if (this.redis) {
      await this.redis.disconnect();
    }
    this.memoryCache.close();
  }

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      // Try Redis first
      if (this.redis && this.redis.status === 'ready') {
        const value = await this.redis.get(key);
        if (value !== null) {
          this.stats.hits++;
          return JSON.parse(value);
        }
      }

      // Fallback to memory cache
      const memValue = this.memoryCache.get<T>(key);
      if (memValue !== undefined) {
        this.stats.hits++;
        return memValue;
      }

      this.stats.misses++;
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      this.stats.misses++;
      return null;
    }
  }

  /**
   * Set value in cache
   */
  async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    const { ttl = 300, useRedis = true } = options;

    try {
      // Store in Redis if available
      if (this.redis && this.redis.status === 'ready' && useRedis) {
        await this.redis.setex(key, ttl, JSON.stringify(value));
      }

      // Always store in memory cache as fallback
      this.memoryCache.set(key, value, ttl);
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete key from cache
   */
  async del(key: string): Promise<boolean> {
    try {
      if (this.redis && this.redis.status === 'ready') {
        await this.redis.del(key);
      }
      this.memoryCache.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Delete multiple keys matching pattern
   */
  async delPattern(pattern: string): Promise<number> {
    let deletedCount = 0;

    try {
      if (this.redis && this.redis.status === 'ready') {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          deletedCount = await this.redis.del(...keys);
        }
      }

      // Clear memory cache keys matching pattern
      const memKeys = this.memoryCache.keys().filter((key: string) =>
        new RegExp(pattern.replace(/\*/g, '.*')).test(key)
      );
      this.memoryCache.del(memKeys);

      return deletedCount + memKeys.length;
    } catch (error) {
      console.error('Cache pattern delete error:', error);
      return 0;
    }
  }

  /**
   * Get or set pattern - fetch from cache or execute function and cache result
   */
  async getOrSet<T>(
    key: string,
    fetchFn: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const value = await fetchFn();
    await this.set(key, value, options);
    return value;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const total = this.stats.hits + this.stats.misses;
    return {
      hits: this.stats.hits,
      misses: this.stats.misses,
      keys: this.memoryCache.keys().length,
      hitRate: total > 0 ? (this.stats.hits / total) * 100 : 0,
    };
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    try {
      if (this.redis && this.redis.status === 'ready') {
        await this.redis.flushdb();
      }
      this.memoryCache.flushAll();
    } catch (error) {
      console.error('Cache clear error:', error);
    }
  }

  /**
   * Check if Redis is available
   */
  isRedisAvailable(): boolean {
    return this.redis && this.redis.status === 'ready';
  }
}