import { Injectable } from '@nestjs/common';
import {
  RequestContext,
  TransactionalConnection,
  Order,
  OrderState,
  PaginatedList
} from '@vendure/core';
import { CacheService } from './cache.service';
import { Brackets } from 'typeorm';

interface OrderQueryOptions {
  ctx: RequestContext;
  keyword?: string;
  state?: OrderState;
  sortKey: string;
  sortDirection: string;
  skip?: number;
  take?: number;
  startDate?: string;
  endDate?: string;
  channelId?: string;
  paymentMethod?: string;
  productIds?: string[];
}

@Injectable()
export class CachedOrderService {
  constructor(
    private connection: TransactionalConnection,
    private cacheService: CacheService
  ) {}

  /**
   * Get orders with caching
   */
  async getOrdersPaginated(options: OrderQueryOptions): Promise<PaginatedList<Order>> {
    const cacheKey = this.generateOrderCacheKey(options);

    return this.cacheService.getOrSet(
      cacheKey,
      () => this.fetchOrdersFromDatabase(options),
      { ttl: 300 } // 5 minutes cache
    );
  }

  /**
   * Get single order with caching
   */
  async getOrderById(ctx: RequestContext, orderId: number): Promise<Order | null> {
    const cacheKey = `order:${orderId}:${ctx.channel.id}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, Order).findOne({
          where: { id: orderId },
          relations: [
            'customer',
            'lines',
            'lines.productVariant',
            'lines.productVariant.translations',
            'payments',
            'fulfillments'
          ]
        });
      },
      { ttl: 600 } // 10 minutes cache for individual orders
    );
  }

  /**
   * Get order by code with caching
   */
  async getOrderByCode(ctx: RequestContext, code: string): Promise<Order | null> {
    const cacheKey = `order:code:${code}:${ctx.channel.id}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, Order).findOne({
          where: { code },
          relations: [
            'customer',
            'lines',
            'lines.productVariant',
            'payments'
          ]
        });
      },
      { ttl: 300 } // 5 minutes cache
    );
  }

  /**
   * Get customer order history with caching
   */
  async getCustomerOrderHistory(ctx: RequestContext, customerId: number): Promise<Order[]> {
    const cacheKey = `customer:${customerId}:orders:${ctx.channel.id}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        const paymentSettledOrders = await this.connection
          .getRepository(ctx, Order)
          .find({
            where: {
              customerId: customerId,
              state: "PaymentSettled",
            },
            order: {
              createdAt: "DESC",
            },
            relations: ["lines", "payments"],
          });

        const codOrders = await this.connection.getRepository(ctx, Order).find({
          where: {
            customerId: customerId,
            state: "PaymentAuthorized",
            payments: {
              method: "cod",
            },
          },
          order: {
            createdAt: "DESC",
          },
          relations: ["lines", "payments"],
        });

        return [...paymentSettledOrders, ...codOrders];
      },
      { ttl: 180 } // 3 minutes cache for order history
    );
  }

  /**
   * Invalidate order cache when order is updated
   */
  async invalidateOrderCache(orderId: number, customerId?: number, code?: string): Promise<void> {
    const patterns = [
      `order:${orderId}:*`,
      `orders:*`, // Invalidate all order list caches
    ];

    if (customerId) {
      patterns.push(`customer:${customerId}:orders:*`);
    }

    if (code) {
      patterns.push(`order:code:${code}:*`);
    }

    // Invalidate all matching patterns
    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }

  private generateOrderCacheKey(options: OrderQueryOptions): string {
    const {
      keyword = '',
      state = '',
      sortKey = 'createdAt',
      sortDirection = 'DESC',
      skip = 0,
      take = 10,
      startDate = '',
      endDate = '',
      channelId = '',
      paymentMethod = '',
      productIds = []
    } = options;

    const keyParts = [
      'orders',
      options.ctx.channel.id,
      keyword,
      state,
      sortKey,
      sortDirection,
      skip,
      take,
      startDate,
      endDate,
      channelId,
      paymentMethod,
      productIds.join(',')
    ];

    return keyParts.join(':');
  }

  private async fetchOrdersFromDatabase(options: OrderQueryOptions): Promise<PaginatedList<Order>> {
    const { ctx, skip = 0, take = 10 } = options;

    const qb = this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder("order")
      .leftJoinAndSelect("order.customer", "customer")
      .leftJoinAndSelect("customer.addresses", "customerAddresses")
      .leftJoinAndSelect("order.lines", "lines")
      .leftJoinAndSelect("order.channels", "orderChannelsChannel")
      .leftJoinAndSelect("lines.productVariant", "productVariant")
      .leftJoinAndSelect(
        "productVariant.translations",
        "productVariantTranslations"
      )
      .leftJoinAndSelect("order.payments", "payments");

    if (ctx.channel.id) {
      qb.andWhere("orderChannelsChannel.id = :channelId", {
        channelId: ctx.channel.id,
      });
    }

    // Apply filters
    if (options.keyword) {
      qb.andWhere(
        new Brackets((qb) => {
          qb.where("order.code ILIKE :keyword", { keyword: `%${options.keyword}%` })
            .orWhere("customer.emailAddress ILIKE :keyword", { keyword: `%${options.keyword}%` })
            .orWhere("customer.phoneNumber ILIKE :keyword", { keyword: `%${options.keyword}%` });
        })
      );
    }

    if (options.state) {
      qb.andWhere("order.state = :state", { state: options.state });
    }

    if (options.paymentMethod) {
      qb.andWhere("payments.method = :paymentMethod", { paymentMethod: options.paymentMethod });
    }

    if (options.startDate) {
      qb.andWhere("order.createdAt >= :startDate", { startDate: options.startDate });
    }

    if (options.endDate) {
      qb.andWhere("order.createdAt <= :endDate", { endDate: options.endDate });
    }

    // Apply sorting
    const sortDirection = options.sortDirection?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    switch (options.sortKey) {
      case 'createdAt':
        qb.orderBy("order.createdAt", sortDirection);
        break;
      case 'total':
        qb.orderBy("order.total", sortDirection);
        break;
      case 'state':
        qb.orderBy("order.state", sortDirection);
        break;
      default:
        qb.orderBy("order.createdAt", 'DESC');
    }

    // Apply pagination
    qb.skip(skip).take(take);

    const [items, totalItems] = await qb.getManyAndCount();

    return {
      items,
      totalItems,
    };
  }
}