import { Injectable } from '@nestjs/common';
import {
  RequestContext,
  TransactionalConnection,
  Product,
  ProductVariant,
  PaginatedList
} from '@vendure/core';
import { CacheService } from './cache.service';

@Injectable()
export class CachedProductService {
  constructor(
    private connection: TransactionalConnection,
    private cacheService: CacheService
  ) {}

  /**
   * Get all enabled product variants with caching
   */
  async getEnabledProductVariants(ctx: RequestContext): Promise<ProductVariant[]> {
    const cacheKey = `products:variants:enabled:${ctx.channel.id}:${ctx.languageCode}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, 'ProductVariant').find({
          where: {
            enabled: true,
            product: { enabled: true }
          },
          relations: [
            'product',
            'product.assets',
            'assets',
            'translations'
          ]
        });
      },
      { ttl: 900 } // 15 minutes cache for product catalog
    );
  }

  /**
   * Get product by ID with caching
   */
  async getProductById(ctx: RequestContext, productId: number): Promise<Product | null> {
    const cacheKey = `product:${productId}:${ctx.channel.id}:${ctx.languageCode}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, Product).findOne({
          where: { id: productId, enabled: true },
          relations: [
            'variants',
            'variants.translations',
            'variants.assets',
            'assets',
            'translations'
          ]
        });
      },
      { ttl: 1800 } // 30 minutes cache for individual products
    );
  }

  /**
   * Get products with custom filtering and caching
   */
  async getCustomProducts(ctx: RequestContext, filters: any): Promise<PaginatedList<Product>> {
    const cacheKey = this.generateProductFilterCacheKey(ctx, filters);

    return this.cacheService.getOrSet(
      cacheKey,
      () => this.fetchProductsFromDatabase(ctx, filters),
      { ttl: 600 } // 10 minutes cache for filtered products
    );
  }

  /**
   * Get product variants by brand with caching
   */
  async getProductsByBrand(ctx: RequestContext, brand: string): Promise<Product[]> {
    const cacheKey = `products:brand:${brand}:${ctx.channel.id}:${ctx.languageCode}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, Product).find({
          where: {
            enabled: true,
            customFields: { brand }
          },
          relations: ['variants', 'translations', 'assets']
        });
      },
      { ttl: 1200 } // 20 minutes cache for brand filtering
    );
  }

  /**
   * Get featured products with caching
   */
  async getFeaturedProducts(ctx: RequestContext, limit: number = 10): Promise<Product[]> {
    const cacheKey = `products:featured:${limit}:${ctx.channel.id}:${ctx.languageCode}`;

    return this.cacheService.getOrSet(
      cacheKey,
      async () => {
        return this.connection.getRepository(ctx, Product).find({
          where: { enabled: true },
          relations: ['variants', 'translations', 'assets'],
          order: { createdAt: 'DESC' },
          take: limit
        });
      },
      { ttl: 3600 } // 1 hour cache for featured products
    );
  }

  /**
   * Invalidate product cache when product is updated
   */
  async invalidateProductCache(productId: number, brand?: string): Promise<void> {
    const patterns = [
      `product:${productId}:*`,
      `products:variants:enabled:*`,
      `products:featured:*`,
      `products:filter:*`, // Invalidate all filtered product caches
    ];

    if (brand) {
      patterns.push(`products:brand:${brand}:*`);
    }

    // Invalidate all matching patterns
    for (const pattern of patterns) {
      await this.cacheService.delPattern(pattern);
    }
  }

  private generateProductFilterCacheKey(ctx: RequestContext, filters: any): string {
    const filterString = JSON.stringify(filters);
    const hash = Buffer.from(filterString).toString('base64').slice(0, 16);
    return `products:filter:${hash}:${ctx.channel.id}:${ctx.languageCode}`;
  }

  private async fetchProductsFromDatabase(ctx: RequestContext, filters: any): Promise<PaginatedList<Product>> {
    const qb = this.connection
      .getRepository(ctx, Product)
      .createQueryBuilder("product");

    // Join translations table for name and slug filtering/sorting
    qb.leftJoinAndSelect("product.translations", "productTranslations");
    qb.andWhere("product.enabled = :enabled", { enabled: true });

    // Apply filters
    if (filters.filter?.id) {
      qb.andWhere("product.id = :id", { id: filters.filter.id });
    }

    if (filters.filter?.createdAt) {
      qb.andWhere("product.createdAt = :createdAt", {
        createdAt: filters.filter.createdAt,
      });
    }

    // Apply sorting
    if (filters.sort) {
      if (filters.sort.createdAt) {
        qb.orderBy("product.createdAt", filters.sort.createdAt);
      }
      if (filters.sort.name) {
        qb.orderBy("productTranslations.name", filters.sort.name);
      }
    }

    // Filter by current language context if available
    if (ctx.languageCode) {
      qb.andWhere("productTranslations.languageCode = :languageCode", {
        languageCode: ctx.languageCode,
      });
    }

    const [products, totalItems] = await qb.getManyAndCount();
    return {
      items: products,
      totalItems,
    };
  }
}