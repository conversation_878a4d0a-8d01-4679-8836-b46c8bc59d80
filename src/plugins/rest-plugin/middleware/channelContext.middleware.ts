import {
  Injectable,
  NestMiddleware,
  BadRequestException,
} from "@nestjs/common";
import { Request, Response, NextFunction } from "express";
import { ChannelService, RequestContext } from "@vendure/core";

@Injectable()
export class ChannelContextMiddleware implements NestMiddleware {
  constructor(private channelService: ChannelService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      // Check headers first, then query params
      const channelToken =
        (req.headers["channel-token"] as string) ||
        (req.query.channelToken as string);

      if (!channelToken) {
        throw new BadRequestException(
          "channelToken is required in headers or query parameters"
        );
      }

      // Create the request context
      const ctx = await this.createChannelRequestContext(channelToken);

      // Attach to request object for controllers to use
      (req as any).vendureContext = ctx;

      next();
    } catch (error) {
      // Make sure errors are properly passed to the exception system
      next(error);
    }
  }

  private async createChannelRequestContext(
    channelToken: string
  ): Promise<RequestContext> {
    // Create a temporary context to get the channel
    const tempCtx = await this.createDefaultRequestContext();

    // Get the channel from token
    const channel = await this.channelService.getChannelFromToken(
      tempCtx,
      channelToken
    );

    if (!channel) {
      // Fallback to default channel
      return tempCtx;
    }

    // Create context with the correct channel
    return new RequestContext({
      apiType: "admin",
      isAuthorized: true,
      authorizedAsOwnerOnly: false,
      channel,
    });
  }

  private async createDefaultRequestContext(): Promise<RequestContext> {
    const channel = await this.channelService.getDefaultChannel();
    return new RequestContext({
      apiType: "admin",
      isAuthorized: true,
      authorizedAsOwnerOnly: false,
      channel,
    });
  }
}
