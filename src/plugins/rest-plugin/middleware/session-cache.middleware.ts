import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { CacheService } from '../services/cache.service';

@Injectable()
export class SessionCacheMiddleware implements NestMiddleware {
  constructor(private cacheService: CacheService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // Extract session/auth token from request
    const authHeader = req.headers.authorization;
    const sessionCookie = req.cookies?.['session'];

    if (authHeader || sessionCookie) {
      const token = authHeader?.replace('Bearer ', '') || sessionCookie;
      const sessionKey = `session:token:${token}`;

      // Try to get cached session data
      const cachedSession = await this.cacheService.get(sessionKey);

      if (cachedSession) {
        // Attach cached session to request
        (req as any).cachedSession = cachedSession;
      }

      // Cache session data on response if not cached
      const originalSend = res.send;
      const cacheService = this.cacheService;

      res.send = function(data: any) {
        // If response contains user/session data and we don't have it cached
        if (!cachedSession && data && typeof data === 'string') {
          try {
            const responseData = JSON.parse(data);
            if (responseData.user || responseData.session) {
              // Cache the session data
              const sessionData = {
                user: responseData.user,
                session: responseData.session,
                timestamp: Date.now()
              };

              // Don't await to avoid blocking response
              cacheService.set(sessionKey, sessionData, { ttl: 1800 }); // 30 minutes
            }
          } catch (e) {
            // Ignore JSON parse errors
          }
        }

        return originalSend.call(this, data);
      };
    }

    next();
  }
}