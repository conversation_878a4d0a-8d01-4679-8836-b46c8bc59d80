import {
  PluginCommonModule,
  VendurePlugin,
  CustomerService,
  ExternalAuthenticationService,
} from "@vendure/core";
import { PaymentCallbackController } from "./api/payment-callback/zalo.controller";
import { TestController } from "./api/test";
import { OrderController } from "./api/order";
import { ChannelController } from "./api/channel";
import { AnalyticController } from "./api/analytic";
import { ProductController } from "./api/products";
import { PointsRedemptionController } from "./api/points-redemption";
import { OrderConfirmationService } from "../email/services/order-confirmation";
import { ValidationService } from "./services/validation.service";
import { CacheService } from "./services/cache.service";
import { CachedOrderService } from "./services/cached-order.service";
import { CachedProductService } from "./services/cached-product.service";
import { CachedAuthService } from "./services/cached-auth.service";
import { AppCacheService } from "./services/app-cache.service";
import { CDNService } from "./services/cdn.service";
import { CacheInvalidationService } from "./services/cache-invalidation.service";
import { CacheMonitorService } from "./services/cache-monitor.service";
import { CacheDashboardController } from "./api/cache-dashboard.controller";
import { MiddlewareConsumer, NestModule } from "@nestjs/common";
import { ChannelContextMiddleware } from "./middleware/channelContext.middleware";
import { SessionCacheMiddleware } from "./middleware/session-cache.middleware";
import { ViettelPostController } from "./api/webhook/viettel-post";
import { DstMallOrderController } from "./api/dst-mall/order.controller";
import { DstMallFulfillmentController } from "./api/dst-mall/fulfillment.controller";

@VendurePlugin({
  imports: [PluginCommonModule],
  controllers: [
    PaymentCallbackController,
    TestController,
    OrderController,
    ChannelController,
    AnalyticController,
    ProductController,
    ViettelPostController,
    PointsRedemptionController,
    DstMallOrderController,
    DstMallFulfillmentController,
    CacheDashboardController,
  ],
  providers: [
    OrderConfirmationService,
    CustomerService,
    ExternalAuthenticationService,
    ValidationService,
    CacheService,
    CachedOrderService,
    CachedProductService,
    CachedAuthService,
    AppCacheService,
    CDNService,
    CacheInvalidationService,
    CacheMonitorService,
  ],
})
export class RestPlugin implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ChannelContextMiddleware)
      .forRoutes(
        "api/v1/orders",
        "api/v1/products",
        "api/v1/analytic",
        "api/v1/points-redemption",
        "api/v1/channels",
        "api/v1/dst-mall/orders",
        "api/v1/dst-mall/fulfillment",
        "api/v1/cache"
      );

    // Apply session cache middleware to all API routes
    consumer
      .apply(SessionCacheMiddleware)
      .forRoutes("api/*");
  }
}
