import {
  RequestContext,
  ChannelService,
  OrderService,
  TransactionalConnection,
  Fulfillment,
  FulfillmentService,
} from "@vendure/core";
import { Logger } from "@vendure/core";
import { Controller, Post, Body } from "@nestjs/common";
import { getSecretKey } from "../../../../utils/getSecretKey";

const loggerCtx = "ViettelPostWebhook";

type ViettelPostWebhookBody = {
  requestTrace: string;
  requestDateTime: number;
  requestParameters: {
    service: string;
    function: string;
    data: {
      orderNumber: string;
      orderReference: string;
      orderStatusCode: number;
      orderStatusDate: string;
      orderStatusDescription: string;
      orderPayment: number;
      orderService: string;
      moneyCollection: number;
      moneyTotal: number;
      moneyFeeCod: number;
      orderServiceAdd: [
        { orderServiceAddCode: string; orderServiceAddValue: number },
        { orderServiceAddCode: string; orderServiceAddValue: number }
      ];
      productWeight: number;
      voucherValue: number;
      receiverFullName: string;
      receiverPhone: string;
      expectedDelivery: string;
      expectedDeliveryDate: string;
      employeeFullName: string;
      employeePhone: string;
      deliveryReasonCode: string | null;
      locationCurrently: string;
      orderNote: string;
      pod: { images: string[] };
      isReturning: boolean;
    };
  };
};

@Controller("api/v1/webhook/viettel-post")
export class ViettelPostController {
  constructor(
    private channelService: ChannelService,
    private orderService: OrderService,
    private connection: TransactionalConnection,
    private fulfillmentService: FulfillmentService
  ) {}

  @Post("")
  async webhook(@Body() body: ViettelPostWebhookBody) {
    Logger.info(`body: ${JSON.stringify(body)}`, loggerCtx);
    const ctx = await this.createRequestContext();

    const orderCode = body.requestParameters.data.orderReference;
    if (!orderCode) {
      Logger.error(`Order code not found: ${orderCode}`, loggerCtx);
      throw new Error("Order code not found");
    }
    const order = await this.orderService.findOneByCode(ctx, orderCode);
    if (!order) {
      Logger.error(`Order not found: ${orderCode}`, loggerCtx);
      throw new Error("Order not found");
    }
    if (order.state === "Delivered") {
      return {
        status: "success",
      };
    }
    const fulfillment = await this.connection
      .getRepository(ctx, Fulfillment)
      .findOne({
        where: {
          trackingCode: body.requestParameters.data.orderNumber,
        },
      });
    let extraData = [];
    if (!fulfillment?.customFields.extraData) {
      extraData = JSON.parse(fulfillment?.customFields.extraData || "[]");
    }
    if (!fulfillment) {
      Logger.error(
        `Fulfillment not found: ${body.requestParameters.data.orderNumber}`,
        loggerCtx
      );
      throw new Error("Fulfillment not found");
    }
    if (body.requestParameters.data.orderStatusCode === 501) {
      await this.fulfillmentService.transitionToState(
        ctx,
        fulfillment.id,
        "Delivered"
      );
    } else if (body.requestParameters.data.orderStatusCode === 105) {
      await this.fulfillmentService.transitionToState(
        ctx,
        fulfillment.id,
        "CarrierReceived"
      );
    } else if (body.requestParameters.data.orderStatusCode === 500) {
      await this.fulfillmentService.transitionToState(
        ctx,
        fulfillment.id,
        "Shipped"
      );
    } else if (
      [503, 504, 201, 107, -15].includes(
        body.requestParameters.data.orderStatusCode
      )
    ) {
      await this.fulfillmentService.transitionToState(
        ctx,
        fulfillment.id,
        "Cancelled"
      );
    }
    await this.connection
      .getRepository(ctx, Fulfillment)
      .update(fulfillment.id, {
        customFields: {
          extraData: JSON.stringify([
            ...extraData,
            body.requestParameters.data,
          ]),
        },
      });
    return {
      status: "success",
    };
  }

  private async createRequestContext(): Promise<RequestContext> {
    const channel = await this.channelService.getDefaultChannel();
    return new RequestContext({
      apiType: "admin",
      isAuthorized: true,
      authorizedAsOwnerOnly: false,
      channel,
    });
  }
}
