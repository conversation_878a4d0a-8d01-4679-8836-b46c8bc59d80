import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Req,
} from '@nestjs/common';
import {
  OrderService,
  ShippingMethodService,
} from '@vendure/core';
import { Logger } from '@vendure/core';

const loggerCtx = 'FulfillmentController';

interface CreateFulfillmentRequest {
  orderCode: string;
  handlerCode: string;
  lines?: Array<{
    orderLineId: string;
    quantity: number;
  }>;
}

@Controller('api/v1/dst-mall/fulfillment')
export class DstMallFulfillmentController {
  constructor(
    private orderService: OrderService,
    private shippingMethodService: ShippingMethodService
  ) { }

  @Post()
  async createFulfillment(@Req() req: any, @Body() body: CreateFulfillmentRequest) {
    try {
      const ctx = req.vendureContext;

      const order = await this.orderService.findOneByCode(ctx, body.orderCode);
      if (!order) {
        throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
      }

      const validOrderStates = ['PaymentSettled', 'PaymentAuthorized', 'PartiallyShipped'];
      if (!validOrderStates.includes(order.state)) {
        throw new HttpException(`Cannot create fulfillment for order in ${order.state} state`, HttpStatus.BAD_REQUEST);
      }

      let fulfillmentLines: Array<{ orderLineId: string; quantity: number }>;

      if (body.lines && body.lines.length > 0) {
        fulfillmentLines = body.lines;

        for (const line of body.lines) {
          const orderLine = order.lines.find(ol => ol.id.toString() === line.orderLineId);
          if (!orderLine) {
            throw new HttpException(`Order line ${line.orderLineId} not found in order`, HttpStatus.BAD_REQUEST);
          }
          if (line.quantity > orderLine.quantity) {
            throw new HttpException(`Quantity ${line.quantity} exceeds available quantity ${orderLine.quantity} for order line ${line.orderLineId}`, HttpStatus.BAD_REQUEST);
          }
        }
      } else {
        fulfillmentLines = order.lines.map(line => ({
          orderLineId: line.id.toString(),
          quantity: line.quantity
        }));
      }

      const shippingMethods = await this.shippingMethodService.findAll(ctx);
      const validHandler = shippingMethods.items.find(method => method.fulfillmentHandlerCode === body.handlerCode);
      if (!validHandler) {
        throw new HttpException(`Invalid fulfillment handler: ${body.handlerCode}`, HttpStatus.BAD_REQUEST);
      }

      const result = await this.orderService.createFulfillment(ctx, {
        lines: fulfillmentLines,
        handler: {
          code: body.handlerCode,
          arguments: [],
        }
      });

      if ('errorCode' in result) {
        throw new HttpException(`Failed to create fulfillment: ${result.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
      }

      Logger.verbose(`Fulfillment created successfully for order ${order.code}`, loggerCtx);

      return {
        success: true,
        result: {
            id: result.id,
            trackingCode: result.trackingCode,
            state: result.state,
            handlerCode: result.handlerCode,
            customFields: result.customFields,
            createdAt: result.createdAt,
        },
      };
    } catch (error: any) {
      Logger.error(`Error creating fulfillment: ${error.message}`, loggerCtx, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
} 