import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Req,
} from '@nestjs/common';
import {
  OrderService,
  ShippingMethodService,
} from '@vendure/core';
import { Logger } from '@vendure/core';

const loggerCtx = 'FulfillmentController';

interface CreateFulfillmentRequest {
  orderCode: string;
  handlerCode: string;
  lines?: Array<{
    orderLineId: string;
    quantity: number;
  }>;
}

interface BatchFulfillmentRequest {
  orders: Array<{
    orderCode: string;
    handlerCode: string;
  }>;
}

interface FulfillmentResult {
  orderCode: string;
  success: boolean;
  message: string;
  fulfillment?: {
    id: any;
    trackingCode?: string;
    state: string;
    handlerCode: string;
    customFields?: any;
    createdAt: Date;
  };
}

interface BatchFulfillmentResponse {
  success: boolean;
  message: string;
  results: FulfillmentResult[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

@Controller('api/v1/dst-mall/fulfillment')
export class DstMallFulfillmentController {
  constructor(
    private orderService: OrderService,
    private shippingMethodService: ShippingMethodService
  ) { }

  @Post()
  async createBatchFulfillment(@Req() req: any, @Body() body: BatchFulfillmentRequest): Promise<BatchFulfillmentResponse> {
    try {
      const ctx = req.vendureContext;

      if (!body.orders || !Array.isArray(body.orders) || body.orders.length === 0) {
        throw new HttpException('Orders list is required and must not be empty', HttpStatus.BAD_REQUEST);
      }

      const maxOrders = 10;
      if (body.orders.length > maxOrders) {
        throw new HttpException(`Cannot process more than ${maxOrders} orders at once`, HttpStatus.BAD_REQUEST);
      }

      const results: FulfillmentResult[] = [];

      const fulfillmentPromises = body.orders.map(async (orderRequest) => {
        return this.processSingleFulfillment(ctx, orderRequest);
      });

      const orderResults = await Promise.all(fulfillmentPromises);
      results.push(...orderResults);

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      Logger.verbose(`Batch fulfillment completed: ${successful} successful, ${failed} failed`, loggerCtx);

      return {
        success: failed === 0,
        message: failed === 0 
          ? `Successfully created fulfillments for all ${successful} orders`
          : `Processed ${results.length} orders: ${successful} successful, ${failed} failed`,
        results,
        summary: {
          total: results.length,
          successful,
          failed
        }
      };
    } catch (error: any) {
      Logger.error(`Error in batch fulfillment: ${error.message}`, loggerCtx, error.stack);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Internal server error', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private async processSingleFulfillment(ctx: any, orderRequest: { orderCode: string; handlerCode: string }): Promise<FulfillmentResult> {
    try {
      const order = await this.orderService.findOneByCode(ctx, orderRequest.orderCode, ['lines']);
      if (!order) {
        return {
          orderCode: orderRequest.orderCode,
          success: false,
          message: 'Order not found'
        };
      }

      const validOrderStates = ['PaymentSettled', 'PaymentAuthorized', 'PartiallyShipped'];
      if (!validOrderStates.includes(order.state)) {
        return {
          orderCode: orderRequest.orderCode,
          success: false,
          message: `Cannot create fulfillment for order in ${order.state} state`
        };
      }

      const fulfillmentLines = order.lines.map(line => ({
        orderLineId: line.id.toString(),
        quantity: line.quantity
      }));

      const shippingMethods = await this.shippingMethodService.findAll(ctx);
      const validHandler = shippingMethods.items.find(method => method.fulfillmentHandlerCode === orderRequest.handlerCode);
      if (!validHandler) {
        return {
          orderCode: orderRequest.orderCode,
          success: false,
          message: `Invalid fulfillment handler: ${orderRequest.handlerCode}`
        };
      }

      const result = await this.orderService.createFulfillment(ctx, {
        lines: fulfillmentLines,
        handler: {
          code: orderRequest.handlerCode,
          arguments: [],
        }
      });

      if ('errorCode' in result) {
        return {
          orderCode: orderRequest.orderCode,
          success: false,
          message: `Failed to create fulfillment: ${result.message}`
        };
      }

      Logger.verbose(`Fulfillment created successfully for order ${order.code}`, loggerCtx);

      return {
        orderCode: orderRequest.orderCode,
        success: true,
        message: 'Fulfillment created successfully',
        fulfillment: {
          id: result.id,
          trackingCode: result.trackingCode,
          state: result.state,
          handlerCode: result.handlerCode,
          customFields: result.customFields,
          createdAt: result.createdAt,
        }
      };
    } catch (error: any) {
      Logger.error(`Error processing fulfillment for order ${orderRequest.orderCode}: ${error.message}`, loggerCtx);
      return {
        orderCode: orderRequest.orderCode,
        success: false,
        message: error.message || 'Internal error'
      };
    }
  }
} 
