import { 
  OrderService, 
  TransactionalConnection,
  Order,
  PaginatedList
} from '@vendure/core';
import { Controller, Get, Post, Req, Query, Body, HttpException, HttpStatus, Res } from '@nestjs/common';
import { DateTime } from 'luxon';
import * as XLSX from 'xlsx';
import { Response } from 'express';

interface CancelOrderRequest {
  orderCodes: string[];
  reason?: string;
}

interface CancelOrderResult {
  orderCode: string;
  success: boolean;
  message: string;
  state?: string;
}

interface CancelOrdersResponse {
  success: boolean;
  message: string;
  results: CancelOrderResult[];
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

@Controller('api/v1/dst-mall/orders')
export class DstMallOrderController {
  constructor(
    private orderService: OrderService,
    private connection: TransactionalConnection
  ) {}

  @Get('')
  async listOrders(
    @Req() req: any,
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
    @Query('search') search?: string,
    @Query('state') state?: string | string[],
    @Query('paymentState') paymentState?: string | string[],
    @Query('paymentMethod') paymentMethod?: string | string[],
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
    @Query('timezone') timezone: string = 'UTC'
  ): Promise<PaginatedList<any>> {
    try {
      const ctx = req.vendureContext;
      const take = Math.min(limit || 10, 100);
      const skip = (page - 1) * take;

      const orderStates = this.normalizeToArray(state);
      const paymentStates = this.normalizeToArray(paymentState);
      const paymentMethods = this.normalizeToArray(paymentMethod);

      const baseQueryBuilder = () => this.connection
        .getRepository(ctx, Order)
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.customer', 'customer')
        .leftJoinAndSelect('order.lines', 'lines')
        .leftJoinAndSelect('lines.productVariant', 'productVariant')
        .leftJoinAndSelect('productVariant.product', 'product')
        .leftJoinAndSelect('productVariant.translations', 'productVariantTranslations')
        .leftJoinAndSelect('product.translations', 'productTranslations')
        .leftJoinAndSelect('order.payments', 'payments')
        .leftJoinAndSelect('order.fulfillments', 'fulfillments')
        .where('order.active = :active', { active: false });

      const baseCountQueryBuilder = () => this.connection
        .getRepository(ctx, Order)
        .createQueryBuilder('order')
        .leftJoin('order.customer', 'customer')
        .leftJoin('order.payments', 'payments')
        .where('order.active = :active', { active: false });

      let queryBuilder = baseQueryBuilder().orderBy('order.orderPlacedAt', 'DESC');
      let countQueryBuilder = baseCountQueryBuilder();

      if (search) {
        const searchCondition = '(order.code = :search OR customer.phoneNumber = :search)';
        queryBuilder = queryBuilder.andWhere(searchCondition, { search });
        countQueryBuilder = countQueryBuilder.andWhere(searchCondition, { search });
      }

      if (orderStates && orderStates.length > 0) {
        queryBuilder = queryBuilder.andWhere('order.state IN (:...orderStates)', { orderStates });
        countQueryBuilder = countQueryBuilder.andWhere('order.state IN (:...orderStates)', { orderStates });
      }

      if (paymentStates && paymentStates.length > 0) {
        queryBuilder = queryBuilder.andWhere('payments.state IN (:...paymentStates)', { paymentStates });
        countQueryBuilder = countQueryBuilder.andWhere('payments.state IN (:...paymentStates)', { paymentStates });
      }

      if (paymentMethods && paymentMethods.length > 0) {
        queryBuilder = queryBuilder.andWhere('payments.method IN (:...paymentMethods)', { paymentMethods });
        countQueryBuilder = countQueryBuilder.andWhere('payments.method IN (:...paymentMethods)', { paymentMethods });
      }

      if (startDate && endDate) {
        const startUtc = DateTime.fromISO(startDate, { zone: timezone }).startOf('day').toUTC();
        const endUtc = DateTime.fromISO(endDate, { zone: timezone }).endOf('day').toUTC();
        const dateFilter = 'order.orderPlacedAt BETWEEN :startDate AND :endDate';
        const dateParams = {
          startDate: startUtc.toJSDate(),
          endDate: endUtc.toJSDate(),
        };  
        
        queryBuilder = queryBuilder.andWhere(dateFilter, dateParams);
        countQueryBuilder = countQueryBuilder.andWhere(dateFilter, dateParams);
      }

      const [orders, totalItems] = await Promise.all([
        queryBuilder.skip(skip).take(take).getMany(),
        countQueryBuilder.getCount()
      ]);

      return {
        items: orders.map(order => this.mapOrderToResponse(order, ctx)),
        totalItems
      };
    } catch (error) {
      console.error('Error in listOrders:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to fetch orders: ${errorMessage}`);
    }
  }

  @Get('export')
  async exportOrders(
    @Req() req: any,
    @Res() res: Response,
    @Query('limit') limit: number = 1000,
    @Query('page') page: number = 1,
    @Query('search') search?: string,
    @Query('state') state?: string | string[],
    @Query('paymentState') paymentState?: string | string[],
    @Query('paymentMethod') paymentMethod?: string | string[],
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string,
    @Query('timezone') timezone: string = 'UTC'
  ) {
    try {
      const ctx = req.vendureContext;
      const take = Math.min(limit || 1000, 1000);
      const skip = (page - 1) * take;

      const orderStates = this.normalizeToArray(state);
      const paymentStates = this.normalizeToArray(paymentState);
      const paymentMethods = this.normalizeToArray(paymentMethod);

      const baseQueryBuilder = () => this.connection
        .getRepository(ctx, Order)
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.customer', 'customer')
        .leftJoinAndSelect('order.lines', 'lines')
        .leftJoinAndSelect('lines.productVariant', 'productVariant')
        .leftJoinAndSelect('productVariant.product', 'product')
        .leftJoinAndSelect('order.payments', 'payments')
        .leftJoinAndSelect('order.fulfillments', 'fulfillments')
        .where('order.active = :active', { active: false });

      let queryBuilder = baseQueryBuilder().orderBy('order.orderPlacedAt', 'DESC');

      if (search) {
        const searchCondition = '(order.code = :search OR customer.phoneNumber = :search)';
        queryBuilder = queryBuilder.andWhere(searchCondition, { search });
      }

      if (orderStates && orderStates.length > 0) {
        queryBuilder = queryBuilder.andWhere('order.state IN (:...orderStates)', { orderStates });
      }

      if (paymentStates && paymentStates.length > 0) {
        queryBuilder = queryBuilder.andWhere('payments.state IN (:...paymentStates)', { paymentStates });
      }

      if (paymentMethods && paymentMethods.length > 0) {
        queryBuilder = queryBuilder.andWhere('payments.method IN (:...paymentMethods)', { paymentMethods });
      }

      if (startDate && endDate) {
        const startUtc = DateTime.fromISO(startDate, { zone: timezone }).startOf('day').toUTC();
        const endUtc = DateTime.fromISO(endDate, { zone: timezone }).endOf('day').toUTC();
        const dateFilter = 'order.orderPlacedAt BETWEEN :startDate AND :endDate';
        const dateParams = {
          startDate: startUtc.toJSDate(),
          endDate: endUtc.toJSDate(),
        };
        queryBuilder = queryBuilder.andWhere(dateFilter, dateParams);
      }

      const orders = await queryBuilder.skip(skip).take(take).getMany();

      // Prepare data for XLSX
      const data = orders.map(order => {
        const fulfillment = order.fulfillments?.[0];
        const payment = order.payments?.[0];
        const shipping = order.shippingAddress as any || {};
        const customer = order.customer as any || {};
        const totalOrderQuantity = order.lines.reduce((sum, line) => sum + line.quantity, 0);
        return {
          orderPlacedAt: order.orderPlacedAt,
          orderCode: order.code,
          fulfillmentTrackingCode: fulfillment?.trackingCode || '',
          customerPhoneNumber: customer.phoneNumber || '',
          customerName: (customer.firstName || '') + ' ' + (customer.lastName || ''),
          totalOrderQuantity,
          paymentMethod: payment?.method || '',
          orderState: order.state,
          paymentState: payment?.state || '',
          shippingFullName: shipping.fullName || '',
          shippingPhoneNumber: shipping.phoneNumber || '',
          shippingAddress: [
            shipping.streetLine1,
            shipping.customFields?.district,
            shipping.customFields?.ward,
            shipping.province
          ].filter(Boolean).join(', '),
          totalOrderCost: order.lines.reduce((sum, line) => sum + line.linePrice, 0),
          finalAmount: order.totalWithTax
        };
      });

      const worksheet = XLSX.utils.json_to_sheet(data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Orders');
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      res.setHeader('Content-Disposition', 'attachment; filename="orders.xlsx"');
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.send(buffer);
    } catch (error) {
      console.error('Error in exportOrders:', error);
      throw new HttpException('Failed to export orders', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('cancel')
  async cancelOrder(
    @Req() req: any,
    @Body() body: CancelOrderRequest
  ): Promise<CancelOrdersResponse> {
    try {
      const ctx = req.vendureContext;

      if (!body.orderCodes || !Array.isArray(body.orderCodes) || body.orderCodes.length === 0) {
        throw new HttpException('Order codes array is required and must not be empty', HttpStatus.BAD_REQUEST);
      }

      const maxOrders = 10;
      if (body.orderCodes.length > maxOrders) {
        throw new HttpException(`Cannot process more than ${maxOrders} orders at once`, HttpStatus.BAD_REQUEST);
      }

      const results: CancelOrderResult[] = [];
      const reason = body.reason || 'Order cancelled via API';

      const cancelPromises = body.orderCodes.map(async (orderCode) => {
        try {
          const order = await this.orderService.findOneByCode(ctx, orderCode, [
            'payments',
            'fulfillments',
            'customer'
          ]);

          if (!order) {
            return {
              orderCode,
              success: false,
              message: 'Order not found'
            };
          }

          if (order.fulfillments && order.fulfillments.length > 0) {
            const hasShippedFulfillments = order.fulfillments.some(
              fulfillment => ['Pending', 'Shipped', 'Delivered', 'PartiallyShipped'].includes(fulfillment.state)
            );
            
            if (hasShippedFulfillments) {
              return {
                orderCode,
                success: false,
                message: 'Cannot cancel order that has been shipped or delivered'
              };
            }
          }

          if (order.state === 'Cancelled') {
            return {
              orderCode,
              success: true,
              message: 'Order is already cancelled',
              state: order.state
            };
          }

          const cancelResult = await this.orderService.cancelOrder(ctx, {
            orderId: order.id,
            reason,
            cancelShipping: true
          });

          if ('errorCode' in cancelResult) {
            return {
              orderCode,
              success: false,
              message: `Failed to cancel order: ${cancelResult.message}`
            };
          }

          return {
            orderCode,
            success: true,
            message: 'Order cancelled successfully',
            state: 'Cancelled'
          };
        } catch (error) {
          return {
            orderCode,
            success: false,
            message: `Error processing order: ${error instanceof Error ? error.message : 'Unknown error'}`
          };
        }
      });

      const orderResults = await Promise.all(cancelPromises);
      results.push(...orderResults);

      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;

      return {
        success: failed === 0,
        message: failed === 0 
          ? `Successfully cancelled all ${successful} orders`
          : `Processed ${results.length} orders: ${successful} successful, ${failed} failed`,
        results,
        summary: {
          total: results.length,
          successful,
          failed
        }
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private mapOrderToResponse(order: Order, ctx: any) {
    const totalOrderCost = order.lines.reduce((sum, line) => {
      return sum + line.linePrice;
    }, 0);

    return {
      id: order.id,
      code: order.code,
      state: order.state,
      totalOrderCost,                  
      shippingFee: order.shipping,     
      finalAmount: order.total, 
      currencyCode: order.currencyCode,
      discounts: order.discounts,      
      customer: {
        email: order.customer?.emailAddress,
        firstName: order.customer?.firstName,
        lastName: order.customer?.lastName,
        phoneNumber: order.customer?.phoneNumber
      },
      orderDate: order.createdAt,
      orderPlacedAt: order.orderPlacedAt,
      shippingAddress: order.shippingAddress,
      lines: order.lines.map(line => {
        const variantName = line.productVariant.translations?.find(
          (t: any) => t.languageCode === ctx.languageCode
        )?.name || line.productVariant.translations?.[0]?.name || line.productVariant.name;

        const productName = line.productVariant.product.translations?.find(
          (t: any) => t.languageCode === ctx.languageCode
        )?.name || line.productVariant.product.translations?.[0]?.name || line.productVariant.product.name;

        // Calculate total discount for this line
        const totalDiscount = line.discounts?.reduce((sum, discount) => sum + Math.abs(discount.amount), 0) || 0;

        // Calculate discounted prices manually
        const discountedLinePrice = line.linePrice - totalDiscount;
        const discountedLinePriceWithTax = line.linePriceWithTax - totalDiscount;
        const discountedUnitPrice = line.unitPrice - (totalDiscount / line.quantity);
        const discountedUnitPriceWithTax = line.unitPriceWithTax - (totalDiscount / line.quantity);

        return {
          productVariantId: line.productVariant.id,
          productId: line.productVariant.product.id,
          productName,
          variantName,
          sku: line.productVariant.sku,
          quantity: line.quantity,
          unitPrice: line.unitPrice,
          unitPriceWithTax: line.unitPriceWithTax,
          linePrice: line.linePrice,
          discounts: line.discounts?.map(discount => ({
            adjustmentSource: discount.adjustmentSource,
            amount: discount.amount,
            amountWithTax: discount.amountWithTax,
            description: discount.description,
            type: discount.type
          })) || [],
          totalDiscount,
          discountedLinePrice,
          discountedLinePriceWithTax,
          discountedUnitPrice,
          discountedUnitPriceWithTax
        };
      }),
      payments: order.payments?.map(payment => ({
        id: payment.id,
        method: payment.method,
        amount: payment.amount,
        state: payment.state,
        settledDate: payment.state === 'Settled' ? payment.updatedAt : null
      })),
      fulfillments: order.fulfillments?.map(fulfillment => ({
        state: fulfillment.state,
        trackingCode: fulfillment.trackingCode,
        handlerCode: fulfillment.handlerCode,
      })) || []
    };
  }

  private normalizeToArray(value: string | string[] | undefined): string[] | undefined {
    if (typeof value === 'string') {
      return value.split(',').map(item => item.trim());
    }
    if (Array.isArray(value)) {
      return value.map(item => item.trim());
    }
    return undefined;
  }
} 