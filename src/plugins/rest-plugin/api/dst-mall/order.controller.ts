import { 
  OrderService, 
  TransactionalConnection,
  Order,
  PaginatedList
} from '@vendure/core';
import { Controller, Get, Post, Req, Query, Body, HttpException, HttpStatus } from '@nestjs/common';

interface CancelOrderRequest {
  orderCode: string;
  reason?: string;
}

@Controller('api/v1/dst-mall/orders')
export class DstMallOrderController {
  constructor(
    private orderService: OrderService,
    private connection: TransactionalConnection
  ) {}

  @Get('')
  async listOrders(
    @Req() req: any,
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
    @Query('search') search?: string,
    @Query('state') state?: string | string[],
    @Query('paymentState') paymentState?: string | string[],
    @Query('paymentMethod') paymentMethod?: string | string[],
    @Query('start_date') startDate?: string,
    @Query('end_date') endDate?: string
  ): Promise<PaginatedList<any>> {
    try {
      const ctx = req.vendureContext;
      const take = Math.min(limit || 10, 100);
      const skip = (page - 1) * take;

      const orderStates = this.normalizeToArray(state);
      const paymentStates = this.normalizeToArray(paymentState);
      const paymentMethods = this.normalizeToArray(paymentMethod);

      const baseQueryBuilder = () => this.connection
        .getRepository(ctx, Order)
        .createQueryBuilder('order')
        .leftJoinAndSelect('order.customer', 'customer')
        .leftJoinAndSelect('order.lines', 'lines')
        .leftJoinAndSelect('lines.productVariant', 'productVariant')
        .leftJoinAndSelect('productVariant.product', 'product')
        .leftJoinAndSelect('productVariant.translations', 'productVariantTranslations')
        .leftJoinAndSelect('product.translations', 'productTranslations')
        .leftJoinAndSelect('order.payments', 'payments')
        .leftJoinAndSelect('order.fulfillments', 'fulfillments')
        .where('order.active = :active', { active: false });

      const baseCountQueryBuilder = () => this.connection
        .getRepository(ctx, Order)
        .createQueryBuilder('order')
        .leftJoin('order.customer', 'customer')
        .leftJoin('order.payments', 'payments')
        .where('order.active = :active', { active: false });

      let queryBuilder = baseQueryBuilder().orderBy('order.createdAt', 'DESC');
      let countQueryBuilder = baseCountQueryBuilder();

      if (search) {
        const searchCondition = '(order.code = :search OR customer.phoneNumber = :search)';
        queryBuilder = queryBuilder.andWhere(searchCondition, { search });
        countQueryBuilder = countQueryBuilder.andWhere(searchCondition, { search });
      }

      if (orderStates && orderStates.length > 0) {
        queryBuilder = queryBuilder.andWhere('order.state IN (:...orderStates)', { orderStates });
        countQueryBuilder = countQueryBuilder.andWhere('order.state IN (:...orderStates)', { orderStates });
      }

      if (paymentStates && paymentStates.length > 0) {
        queryBuilder = queryBuilder.andWhere('payments.state IN (:...paymentStates)', { paymentStates });
        countQueryBuilder = countQueryBuilder.andWhere('payments.state IN (:...paymentStates)', { paymentStates });
      }

      if (paymentMethods && paymentMethods.length > 0) {
        queryBuilder = queryBuilder.andWhere('payments.method IN (:...paymentMethods)', { paymentMethods });
        countQueryBuilder = countQueryBuilder.andWhere('payments.method IN (:...paymentMethods)', { paymentMethods });
      }

      if (startDate && endDate) {
        const dateFilter = 'order.createdAt BETWEEN :startDate AND :endDate';
        const dateParams = {
          startDate: new Date(startDate),
          endDate: new Date(endDate),
        };
        queryBuilder = queryBuilder.andWhere(dateFilter, dateParams);
        countQueryBuilder = countQueryBuilder.andWhere(dateFilter, dateParams);
      }

      const [orders, totalItems] = await Promise.all([
        queryBuilder.skip(skip).take(take).getMany(),
        countQueryBuilder.getCount()
      ]);

      return {
        items: orders.map(order => this.mapOrderToResponse(order, ctx)),
        totalItems
      };
    } catch (error) {
      console.error('Error in listOrders:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to fetch orders: ${errorMessage}`);
    }
  }

  @Post('cancel')
  async cancelOrder(
    @Req() req: any,
    @Body() body: CancelOrderRequest
  ) {
    try {
      const ctx = req.vendureContext;

      if (!body.orderCode) {
        throw new HttpException('Order code is required', HttpStatus.BAD_REQUEST);
      }

      const order = await this.orderService.findOneByCode(ctx, body.orderCode, [
        'payments',
        'fulfillments',
        'customer'
      ]);

      if (!order) {
        throw new HttpException('Order not found', HttpStatus.NOT_FOUND);
      }

      const cancellableStates = ['PaymentAuthorized', 'PaymentSettled'];
      const nonCancellableStates = ['PartiallyShipped', 'Shipped', 'PartiallyFulfilled', 'Fulfilled', 'Delivered'];
      
      if (!cancellableStates.includes(order.state)) {
        throw new HttpException(
          `Cannot cancel order in ${order.state} state. Order must be in one of: ${cancellableStates.join(', ')}`,
          HttpStatus.BAD_REQUEST
        );
      }
      
      if (nonCancellableStates.includes(order.state)) {
        throw new HttpException(
          `Cannot cancel order that has been fulfilled. Order is in ${order.state} state.`,
          HttpStatus.BAD_REQUEST
        );
      }

      if (order.state === 'Cancelled') {
        return {
          success: true,
          message: 'Order is already cancelled',
          order: {
            id: order.id,
            code: order.code,
            state: order.state
          }
        };
      }

      const cancelResult = await this.orderService.cancelOrder(ctx, {
        orderId: order.id,
        reason: body.reason || 'Order cancelled via API',
        cancelShipping: true
      });

      if ('errorCode' in cancelResult) {
        throw new HttpException(
          `Failed to cancel order: ${cancelResult.message}`,
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      return {
        success: true,
        message: 'Order cancelled successfully',
        order: {
          code: order.code,
          state: 'Cancelled',
          reason: body.reason || ''
        }
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      throw new HttpException(
        `Internal server error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  private mapOrderToResponse(order: Order, ctx: any) {
    const totalOrderCost = order.lines.reduce((sum, line) => {
      return sum + line.linePrice;
    }, 0);

    return {
      id: order.id,
      code: order.code,
      state: order.state,
      totalOrderCost,                  
      shippingFee: order.shipping,     
      finalAmount: order.totalWithTax, 
      currencyCode: order.currencyCode,
      discounts: order.discounts,      
      customer: {
        email: order.customer?.emailAddress,
        firstName: order.customer?.firstName,
        lastName: order.customer?.lastName,
        phoneNumber: order.customer?.phoneNumber
      },
      orderDate: order.createdAt,
      shippingAddress: order.shippingAddress,
      lines: order.lines.map(line => {
        const variantName = line.productVariant.translations?.find(
          (t: any) => t.languageCode === ctx.languageCode
        )?.name || line.productVariant.translations?.[0]?.name || line.productVariant.name;

        const productName = line.productVariant.product.translations?.find(
          (t: any) => t.languageCode === ctx.languageCode
        )?.name || line.productVariant.product.translations?.[0]?.name || line.productVariant.product.name;

        // Calculate total discount for this line
        const totalDiscount = line.discounts?.reduce((sum, discount) => sum + Math.abs(discount.amount), 0) || 0;

        // Calculate discounted prices manually
        const discountedLinePrice = line.linePrice - totalDiscount;
        const discountedLinePriceWithTax = line.linePriceWithTax - totalDiscount;
        const discountedUnitPrice = line.unitPrice - (totalDiscount / line.quantity);
        const discountedUnitPriceWithTax = line.unitPriceWithTax - (totalDiscount / line.quantity);

        return {
          productVariantId: line.productVariant.id,
          productId: line.productVariant.product.id,
          productName,
          variantName,
          sku: line.productVariant.sku,
          quantity: line.quantity,
          unitPrice: line.unitPrice,
          unitPriceWithTax: line.unitPriceWithTax,
          linePrice: line.linePrice,
          discounts: line.discounts?.map(discount => ({
            adjustmentSource: discount.adjustmentSource,
            amount: discount.amount,
            amountWithTax: discount.amountWithTax,
            description: discount.description,
            type: discount.type
          })) || [],
          totalDiscount,
          discountedLinePrice,
          discountedLinePriceWithTax,
          discountedUnitPrice,
          discountedUnitPriceWithTax
        };
      }),
      payments: order.payments?.map(payment => ({
        id: payment.id,
        method: payment.method,
        amount: payment.amount,
        state: payment.state,
        settledDate: payment.state === 'Settled' ? payment.updatedAt : null
      })),
      fulfillments: order.fulfillments?.map(fulfillment => ({
        state: fulfillment.state,
        trackingCode: fulfillment.trackingCode,
        handlerCode: fulfillment.handlerCode,
      })) || []
    };
  }

  private normalizeToArray(value: string | string[] | undefined): string[] | undefined {
    if (typeof value === 'string') {
      return value.split(',').map(item => item.trim());
    }
    if (Array.isArray(value)) {
      return value.map(item => item.trim());
    }
    return undefined;
  }
} 