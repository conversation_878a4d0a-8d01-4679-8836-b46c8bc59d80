import { RequestContext, ChannelService, OrderState } from "@vendure/core";
import { Controller, Get, Query } from "@nestjs/common";

@Controller("api/v1/channels")
export class ChannelController {
  constructor(private channelService: ChannelService) {}

  @Get("")
  async listOrders(
    @Query("limit") limit: number,
    @Query("offset") offset: number
  ) {
    const ctx = await this.createRequestContext();
    const channels = await this.channelService.findAll(ctx, {
      skip: offset,
      take: limit,
      filter: {
        code: {
          notEq: "__default_channel__",
        },
      },
    });
    return channels;
  }

  private async createRequestContext(): Promise<RequestContext> {
    const channel = await this.channelService.getDefaultChannel();
    return new RequestContext({
      apiType: "admin",
      isAuthorized: true,
      authorizedAsOwnerOnly: false,
      channel,
    });
  }
}
