import {
  Order,
  OrderService,
  OrderState,
  TransactionalConnection,
} from "@vendure/core";
import { Controller, Get, Query, Req } from "@nestjs/common";
import { In, Between } from "typeorm";

type ChartData = {
  [key: string]: { date: string; totalRevenue: number }[];
};

type AnalyticResponse = {
  totalRevenue: number;
  totalOrders: number;
  totalProducts: number;
  chartData: ChartData;
  products: {
    [key: string]: {
      revenue: number;
      quantity: number;
    };
  };
};

@Controller("api/v1/analytic")
export class AnalyticController {
  constructor(
    private orderService: OrderService,
    private connection: TransactionalConnection
  ) {}

  @Get("")
  async listOrders(
    @Req() req: any,
    @Query("from") from: string,
    @Query("to") to: string,
    @Query("state") state: OrderState,
    @Query("productIds[]") productIds?: string | string[]
  ) {
    const ctx = req.vendureContext;

    let orders: Order[] = [];
    if (productIds) {
      // Convert to array if it's a single string
      const productIdsArray = Array.isArray(productIds)
        ? productIds
        : [productIds];

      orders = await this.connection.getRepository(ctx, Order).find({
        relations: ["lines", "lines.productVariant"],
        where: {
          lines: {
            productVariant: { productId: In(productIdsArray) },
          },
          state: state,
          createdAt: Between(new Date(from), new Date(to)),
        },
      });
    } else {
      const ordersFilter = await this.orderService.findAll(ctx, {
        filter: {
          createdAt: {
            between: {
              start: from,
              end: to,
            },
          },
          state: {
            eq: state,
          },
        },
      });
      orders = ordersFilter.items;
    }

    const chartData: ChartData = {};
    const products: { [key: string]: { revenue: number; quantity: number } } =
      {};
    for (const order of orders) {
      for (const line of order.lines) {
        const productName = line.productVariant.translations[0].name;
        if (!chartData[productName]) {
          chartData[productName] = [];
        }
        chartData[productName].push({
          date: order.createdAt.toISOString(),
          totalRevenue: line.linePrice * line.quantity,
        });
        if (!products[productName]) {
          products[productName] = { revenue: 0, quantity: 0 };
        }
        products[productName].revenue += line.linePrice * line.quantity;
        products[productName].quantity += line.quantity;
      }
    }
    const analytic: AnalyticResponse = {
      totalRevenue: orders.reduce((acc, order) => {
        return acc + order.total;
      }, 0),
      totalOrders: orders.length,
      totalProducts: orders.reduce((acc, order) => {
        return (
          acc +
          order.lines.reduce((acc, line) => {
            return acc + line.quantity;
          }, 0)
        );
      }, 0),
      chartData,
      products,
    };
    return analytic;
  }
}
