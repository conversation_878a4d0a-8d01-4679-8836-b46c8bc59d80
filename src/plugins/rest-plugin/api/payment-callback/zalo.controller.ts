import {
  PluginCommonModule,
  VendurePlugin,
  OrderService,
  PaymentService,
  TransactionalConnection,
  RequestContext,
  Order,
  ChannelService,
  Payment,
} from "@vendure/core";
import { Controller, Post, Body, Headers } from "@nestjs/common";
import { OrderConfirmationService } from "../../../email/services/order-confirmation";

@Controller("payment-callback/zalo")
export class PaymentCallbackController {
  constructor(
    private orderService: OrderService,
    private paymentService: PaymentService,
    private connection: TransactionalConnection,
    private channelService: ChannelService,
    private orderConfirmationService: OrderConfirmationService
  ) {}

  @Post("")
  async handleZaloCallback(@Body() body: any, @Headers() headers: any) {
    try {
      console.log(12312321, body);

      // 1. Verify the webhook signature from Zalo

      // 2. Create RequestContext (required for Vendure operations)
      const ctx = await this.createRequestContext();
      const data = JSON.parse(body.data);

      // 3. Get the order
      const orderCode = data.app_trans_id.split("_")[1];
      const order = await this.orderService.findOneByCode(ctx, orderCode);
      if (!order) {
        return { success: false, message: "Order not found" };
      }

      // 4. Get the payment
      const payment = await this.connection
        .getRepository(ctx, Payment)
        .findOne({
          where: {
            order: {
              id: order.id,
            },
          },
        });
      if (!payment) {
        return { success: false, message: "Payment not found" };
      }

      // 5. Settle the payment if transaction was successful
      await this.connection.getRepository(ctx, Payment).update(payment.id, {
        state: "Settled",
        transactionId: data.zp_trans_id,
      });
      await this.orderService.transitionToState(
        ctx,
        order.id,
        "PaymentSettled"
      );

      await this.connection.getRepository(ctx, Order).update(order.id, {
        orderPlacedAt: new Date(),
      });

      this.orderConfirmationService.sendOrderConfirmation(ctx, order.id);
      return { success: true };
    } catch (error) {
      console.log("error", error);
      return { success: false, message: error };
    }
  }

  private async createRequestContext(): Promise<RequestContext> {
    const channel = await this.channelService.getDefaultChannel();
    return new RequestContext({
      apiType: "admin",
      isAuthorized: true,
      authorizedAsOwnerOnly: false,
      channel,
    });
  }
}
