import { ProductService, ProductVariantService, TransactionalConnection } from '@vendure/core';
import { Controller, Get, Req, Param, NotFoundException } from '@nestjs/common';

@Controller('api/v1/products')
export class ProductController {
  constructor(
    private productService: ProductService,
    private productVariantService: ProductVariantService,
    private connection: TransactionalConnection
  ) { }

  @Get('')
  async listProducts(@Req() req: any) {
    const ctx = req.vendureContext;

    const variants = await this.connection.getRepository(ctx, 'ProductVariant').find({
      where: {
        enabled: true,
        product: { enabled: true }
      },
      relations: [
        'featuredAsset',
        'product',
        'product.assets',
        'product.featuredAsset',
        'assets',
        'translations'
      ]
    });

    const pricingPromises = variants.map(variant =>
      this.productVariantService.findOne(ctx, variant.id, [])
    );
    const variantsWithPricing = await Promise.all(pricingPromises);

    return variants.map((variant, index) => {
      const variantWithPricing = variantsWithPricing[index];
      if (!variantWithPricing) return null;

      const variantName =
        variant.translations?.find(
          (t: any) => t.languageCode === ctx.languageCode
        )?.name ||
        variant.translations?.[0]?.name ||
        variant.name;

      const asset =
        variant.featuredAsset?.source || variant.product?.featuredAsset?.source;

      return {
        productId: variant.product.id,
        variantId: variant.id,
        variantName,
        sku: variant.sku,
        price: variantWithPricing.price,
        priceWithTax: variantWithPricing.priceWithTax,
        asset: `${process.env.ASSETS_HOST}${asset}`,
      };
    }).filter(Boolean);
  }

  @Get('variant/:variantId')
  async getProductVariant(@Req() req: any, @Param('variantId') variantId: string) {
    const ctx = req.vendureContext;

    try {
      const variant = await this.productVariantService.findOne(ctx, variantId, ['product']);

      if (!variant) {
        throw new NotFoundException('Product variant not found');
      }

      const saleableStockLevel = await this.productVariantService.getSaleableStockLevel(ctx, variant);

      return {
        id: variant.id,
        name: variant.name,
        sku: variant.sku,
        enabled: variant.enabled,
        deletedAt: variant.deletedAt,
        price: variant.price,
        stock: saleableStockLevel,
        product: {
          id: variant.product.id,
          name: variant.product.name,
          enabled: variant.product.enabled,
          deletedAt: variant.product.deletedAt,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException('Product variant not found');
    }
  }
}
