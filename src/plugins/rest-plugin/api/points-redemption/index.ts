import { RequestContext, Order, Payment, User, OrderService, CustomerService, TransactionalConnection, ChannelService, Transaction, Customer, ListQueryOptions, translateEntity, ProductVariantService, StockLevelService } from '@vendure/core';
import { Controller, Post, Body, Get, Param, Query } from '@nestjs/common';
import { OrderConfirmationService } from '../../../email/services/order-confirmation';
import { format } from 'date-fns';
import { In } from 'typeorm';

interface PointsRedemptionRequest {
  pointsAppUserId: string;
  pointsUsed: number;
  redemptionToken: string;
  customerData: {
    email: string;
    fullName: string;
    phoneNumber?: string;
  };
  shippingAddress: {
    fullName: string;
    streetLine1: string;
    province: string;
    countryCode: string;
    phoneNumber: string;
    customFields: {
      district?: string;
      ward?: string;
    };
  };
  items: Array<{
    productVariantId: string;
    quantity: number;
  }>;
  channelToken?: string;
}

@Controller('api/v1/points-redemption')
export class PointsRedemptionController {
  constructor(
    private orderService: OrderService,
    private customerService: CustomerService,
    private connection: TransactionalConnection,
    private channelService: ChannelService,
    private orderConfirmationService: OrderConfirmationService,
    private productVariantService: ProductVariantService,
    private stockLevelService: StockLevelService
  ) {}

  @Post('create-order')
  async createPointsRedemptionOrder(@Body() redemptionData: PointsRedemptionRequest) {
    try {
      const ctx = await this.createAdminContext(redemptionData.channelToken);

      return await this.connection.withTransaction(ctx, async transactionalCtx => {
        const customer = await this.findOrCreateCustomer(transactionalCtx, redemptionData.customerData, redemptionData.pointsAppUserId);

        if (!customer.user) {
          throw new Error('Customer user not found');
        }

        const order = await this.orderService.create(transactionalCtx, customer.user.id);

        for (const item of redemptionData.items) {
          const addItemResult = await this.orderService.addItemToOrder(transactionalCtx, order.id, item.productVariantId, item.quantity);

          if ('errorCode' in addItemResult) {
            throw new Error(`Failed to add item ${item.productVariantId}: ${addItemResult.message}`);
          }
        }

        const updatedOrder = await this.orderService.findOne(transactionalCtx, order.id);
        if (!updatedOrder) {
          throw new Error('Failed to find order after adding items');
        }

        await this.orderService.setShippingAddress(transactionalCtx, order.id, redemptionData.shippingAddress);

        await this.createPointsPaymentRecord(transactionalCtx, updatedOrder, redemptionData);

        await this.connection.getRepository(transactionalCtx, Order).update(order.id, {
          state: 'PaymentSettled',
          active: false,
        });

        const payment = await this.connection.getRepository(transactionalCtx, Payment).findOne({
          where: { order: { id: order.id } },
        });

        if (payment) {
          await this.connection.getRepository(transactionalCtx, Payment).update(payment.id, {
            state: 'Settled',
          });
        }

        try {
          await this.orderConfirmationService.sendOrderConfirmation(transactionalCtx, order.id);
        } catch (emailError) {
          console.error('Failed to send confirmation email:', emailError);
        }

        const finalOrder = await this.orderService.findOne(transactionalCtx, order.id);

        if (!finalOrder) {
          throw new Error('Failed to retrieve created order');
        }

        return {
          success: true,
          order: {
            id: finalOrder.id,
            orderCode: finalOrder.code,
            state: finalOrder.state,
            total: finalOrder.total,
            subTotal: finalOrder.subTotal,
          },
        };
      });
    } catch (error) {
      console.error('Points redemption failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  @Get('user-orders/:externalUserId')
  async getUserOrderHistory(
    @Param('externalUserId') externalUserId: string,
    @Query('state') state?: string,
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '10'
  ) {
    try {
      const ctx = await this.createAdminContext();
      const pageNum = Math.max(1, parseInt(page, 10) || 1);
      const limitNum = Math.max(1, parseInt(limit, 10) || 10);
      const skip = (pageNum - 1) * limitNum;

      let stateList: string[] = [];
      if (state) {
        stateList = state.split(',').map(s => s.trim()).filter(Boolean);
      }

      const customer = await this.connection.getRepository(ctx, Customer)
        .createQueryBuilder('customer')
        .where('customer.customFieldsExternaluserid = :externalUserId', { externalUserId })
        .getOne();

      if (!customer) {
        return {
          success: true,
          totalOrders: 0,
          orders: [],
          page: pageNum,
          limit: limitNum,
          totalPages: 0,
        };
      }

      const where: any = { customerId: customer.id };
      if (stateList.length > 1) {
        where.state = In(stateList);
      } else if (stateList.length === 1) {
        where.state = stateList[0];
      }

      const totalOrders = await this.connection.getRepository(ctx, Order).count({ where });

      const orders = await this.connection.getRepository(ctx, Order).find({
        where,
        order: { createdAt: 'DESC' },
        skip,
        take: limitNum,
        relations: [
          'lines',
          'lines.productVariant',
          'lines.productVariant.product',
          'lines.productVariant.product.featuredAsset',
          'customer',
          'payments',
        ],
      });

      return {
        success: true,
        totalOrders,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(totalOrders / limitNum),
        orders: orders.map(order => ({
          code: order.code,
          state: order.state,
          total: order.total,
          product:
            order.lines?.map(line => ({
              name:
                Array.isArray(line.productVariant?.product?.translations) &&
                line.productVariant.product.translations.length > 0
                  ? line.productVariant.product.translations[0].name
                  : null,
              featuredAsset: line.productVariant?.product?.featuredAsset?.preview
                ? `${process.env.ASSETS_HOST}${line.productVariant.product.featuredAsset.preview}`
                : null,
            })) || [],
          productVariants:
            order.lines?.map(line => ({
              name: line.productVariant?.name,
              sku: line.productVariant?.sku,
              featuredAsset: line.productVariant?.product?.featuredAsset?.preview
                ? `${process.env.ASSETS_HOST}${line.productVariant.product.featuredAsset.preview}`
                : null,
            })) || [],
          payments:
            order.payments?.map(payment => ({
              id: payment.id,
              method: payment.method,
              amount: payment.amount,
              state: payment.state,
              metadata: payment.metadata,
            })) || [],
        })),
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { success: false, error: errorMessage };
    }
  }

  @Get('product-variant/:variantId')
  async getProductVariantDetail(@Param('variantId') variantId: string) {
    try {
      const ctx = await this.createAdminContext();
      
      const variant = await this.productVariantService.findOne(ctx, variantId, [
        'product',
        'product.featuredAsset',
        'product.translations',
        'featuredAsset',
        'options',
        'options.translations',
        'options.group',
        'options.group.translations',
      ]);
      
      if (!variant) {
        return { success: false, error: 'Product variant not found' };
      }

      const availableStock = await this.productVariantService.getSaleableStockLevel(ctx, variant);

      return {
        success: true,
        variant: {
          id: variant.id,
          name: variant.name,
          sku: variant.sku,
          featuredAsset: variant.featuredAsset && variant.featuredAsset.preview ? `${process.env.ASSETS_HOST}${variant.featuredAsset.preview}` : null,
          productVariantPrices: variant.productVariantPrices,
          availableStock: availableStock,
          options: (variant.options || []).map((option: any) => ({
            code: option.code,
            name: translateEntity(option, ctx.languageCode).name,
            group: option.group
              ? {
                  id: option.group.id,
                  code: option.group.code,
                  name: translateEntity(option.group, ctx.languageCode).name,
                }
              : null,
          })),
          product: {
            id: variant.product?.id,
            name: variant.product?.name,
            description: variant.product?.translations,
            featuredAsset: variant.product?.featuredAsset && variant.product?.featuredAsset.preview ? `${process.env.ASSETS_HOST}${variant.product.featuredAsset.preview}` : null,
            customFields: variant.product?.customFields,
          },
        },
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { success: false, error: errorMessage };
    }
  }

  @Post('stock-availability')
  async getStockAvailability(@Body() request: { variantIds: string[] }) {
    try {
      const ctx = await this.createAdminContext();
      
      if (!request.variantIds || !Array.isArray(request.variantIds) || request.variantIds.length === 0) {
        return { 
          success: false, 
          error: 'variantIds array is required and must not be empty' 
        };
      }

      const uniqueVariantIds = [...new Set(request.variantIds)];
      const variants = await this.productVariantService.findByIds(ctx, uniqueVariantIds as any[]);
      
      const variantMap = new Map(variants.map(variant => [variant.id.toString(), variant]));
      
      const stockLevels = await Promise.all(
        variants.map(variant => 
          this.productVariantService.getDisplayStockLevel(ctx, variant)
        )
      );

      const stockMap = new Map(
        variants.map((variant, index) => [variant.id.toString(), stockLevels[index]])
      );

      const stockAvailability = request.variantIds.map(variantId => {
        const variant = variantMap.get(variantId);
        
        if (!variant) {
          return {
            variantId,
            error: 'Variant not found',
            stockLevel: 'OUT_OF_STOCK'
          };
        }

        return {
          variantId,
          stockLevel: stockMap.get(variantId) || 'OUT_OF_STOCK'
        };
      });

      return {
        success: true,
        stockAvailability,
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return { success: false, error: errorMessage };
    }
  }

  private async findOrCreateCustomer(ctx: RequestContext, customerData: any, pointsAppUserId: string) {
    try {
      // First, try to find existing customer by externalUserId using raw query
      const existingCustomerByExternalId = await this.connection
        .getRepository(ctx, Customer)
        .createQueryBuilder('customer')
        .where('customer.customFieldsExternaluserid = :externalUserId', { externalUserId: pointsAppUserId })
        .leftJoinAndSelect('customer.user', 'user')
        .getOne();

      if (existingCustomerByExternalId) {
        return existingCustomerByExternalId;
      }

      // Generate email if not provided
      let email = customerData.email;
      if (!email && customerData.phoneNumber) {
        email = this.generateEmailFromPhone(customerData.phoneNumber, pointsAppUserId);
      }

      const existingUser = await this.connection.getRepository(ctx, User).findOne({
        where: { identifier: email },
      });

      if (existingUser) {
        const customer = await this.customerService.findOneByUserId(ctx, existingUser.id);
        if (customer) {
          // Update existing customer with externalUserId if not set
          const currentCustomFields = (customer.customFields as any) || {};
          if (!currentCustomFields.externalUserId) {
            await this.customerService.update(ctx, {
              id: customer.id,
              customFields: {
                ...currentCustomFields,
                externalUserId: pointsAppUserId,
              },
            });
          }
          return customer;
        }
      }

      const createResult = await this.customerService.create(ctx, {
        emailAddress: email,
        lastName: customerData.fullName?.split(' ')[0] || 'Points',
        firstName: customerData.fullName?.split(' ').slice(1).join(' ') || 'User',
        phoneNumber: customerData.phoneNumber,
        customFields: {
          externalUserId: pointsAppUserId,
        },
      });

      if ('errorCode' in createResult) {
        throw new Error(`Failed to create customer: ${createResult.message}`);
      }

      return createResult;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to handle customer: ${errorMessage}`);
    }
  }

  private generateEmailFromPhone(phoneNumber: string, pointsAppUserId: string): string {
    // Clean phone number (remove spaces, dashes, etc.)
    const cleanPhone = phoneNumber.replace(/[^\d]/g, '');

    if (cleanPhone) {
      return `points-${cleanPhone}@email.internal`;
    }

    // Fallback to user ID if phone is invalid
    return `points-user-${pointsAppUserId}@email.internal`;
  }

  private async createPointsPaymentRecord(ctx: RequestContext, order: Order, redemptionData: PointsRedemptionRequest) {
    try {
      const transactionId = `${format(new Date(), 'yyMMdd')}_${order.code}`;

      const payment = this.connection.getRepository(ctx, Payment).create({
        order,
        method: 'points_redemption',
        amount: order.total,
        state: 'Settled',
        transactionId,
        metadata: {
          pointsUsed: redemptionData.pointsUsed,
          externalUserId: redemptionData.pointsAppUserId,
          redemptionToken: redemptionData.redemptionToken,
          customerEmail: redemptionData.customerData.email,
        },
      });

      await this.connection.getRepository(ctx, Payment).save(payment);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to create payment record: ${errorMessage}`);
    }
  }

  private async createAdminContext(channelToken?: string): Promise<RequestContext> {
    try {
      const channel = channelToken ? await this.channelService.getChannelFromToken(channelToken) : await this.channelService.getDefaultChannel();

      return new RequestContext({
        apiType: 'admin',
        isAuthorized: true,
        authorizedAsOwnerOnly: false,
        channel,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new Error(`Failed to create admin context: ${errorMessage}`);
    }
  }
}
