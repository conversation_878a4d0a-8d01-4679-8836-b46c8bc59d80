import {
  RequestContext,
  ChannelService,
  ExternalAuthenticationService,
  CustomerService,
  UserService,
  OrderService,
  TransactionalConnection,
  Customer,
  Order,
  User,
  OrderLine,
  CustomerGroupService,
  PaymentService,
  Payment,
} from "@vendure/core";
import { Controller, Get } from "@nestjs/common";
import { OrderConfirmationService } from "../../email/services/order-confirmation";
import * as XLSX from "xlsx";
import { parse } from "date-fns";

@Controller("test")
export class TestController {
  constructor(
    private channelService: ChannelService,
    private orderConfirmationService: OrderConfirmationService,
    private externalAuthenticationService: ExternalAuthenticationService,
    private customerService: CustomerService,
    private customerGroupService: CustomerGroupService,
    private orderService: OrderService,
    private connection: TransactionalConnection,
    private paymentService: PaymentService
  ) {}

  @Get("confirm-order")
  async confirmOrder() {
    const ctx = await this.createRequestContext();

    this.orderConfirmationService.sendOrderConfirmation(ctx, 132);
  }

  @Get("test")
  async test() {
    const ctx = await this.createRequestContext();

    return await this.connection.getRepository(ctx, Order).find({
      where: {
        id: "141",
      },
      relations: ["lines"],
    });
  }

  @Get("migrate-order")
  async migrateOrder() {
    const ctx = await this.createRequestContext();

    const workbook = XLSX.readFile(
      "/Users/<USER>/Projects/eventista/eventista-vendure/MigrateOrders.xlsx"
    );
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const sheetData = XLSX.utils.sheet_to_json(sheet);

    const productMap = {
      "Infinity8 -The 4th Album": 55,
      "Infinity8 -Infinity8 - T-shirt": 106,
    };
    const userMapProductVariant = new Map();

    for (const row of sheetData) {
      const productVariantId =
        productMap[(row as any)["Sản phẩm"] as keyof typeof productMap];
      const email = (row as any)["Thông tin khách hàng"];
      const address = (row as any)["Địa chỉ nhận vé"];
      const phone = (row as any)["Số điện thoại"];
      const orderCode = (row as any)["Mã đơn hàng"];
      const createdAt = (row as any)["Thời gian giao dịch"];
      const price = (row as any)["Giá vé"];
      const discountAmount = (row as any)["Giảm giá"];
      const priceAfterDiscount = (row as any)["Giá vé sau KM"];
      const discountName = (row as any)["Mã khuyến mại"];
      const parseTime = parse(createdAt, "dd/MM/yyyy HH:mm:ss", new Date());
      parseTime.setHours(parseTime.getHours() - 7);

      if (!userMapProductVariant.has(email)) {
        userMapProductVariant.set(email, {
          orders: [
            {
              code: orderCode,
              createdAt: parseTime,
              discountName,
              products: [
                {
                  productVariantId,
                  quantity: 1,
                  price,
                  priceAfterDiscount,
                  discountAmount,
                },
              ],
            },
          ],
          firstName: (row as any)["First Name"],
          lastName: (row as any)["Last Name"],
          address,
          phone,
        });
      } else {
        const item = userMapProductVariant
          .get(email)
          .orders.find((item: any) => item.code === orderCode);
        if (item) {
          if (
            item.products.find(
              (item: any) => item.productVariantId === productVariantId
            )
          ) {
            item.products.find(
              (item: any) => item.productVariantId === productVariantId
            ).quantity += 1;
          } else {
            item.products.push({
              productVariantId,
              quantity: 1,
              price,
              priceAfterDiscount,
              discountAmount,
            });
          }
        } else {
          userMapProductVariant.get(email).orders.push({
            code: orderCode,
            discountName,
            createdAt: parseTime,
            products: [
              {
                productVariantId,
                quantity: 1,
                price,
                priceAfterDiscount,
                discountAmount,
              },
            ],
          });
        }
      }
    }
    // console.log(2222, [...userMapProductVariant]);

    for (const [email, data] of userMapProductVariant.entries()) {
      await this.externalAuthenticationService.createCustomerAndUser(ctx, {
        strategy: "eventista",
        externalIdentifier: "",
        verified: true,
        emailAddress: email,
        firstName: data.firstName,
        lastName: data.lastName,
      });
      const user = await this.connection.getRepository(ctx, User).findOne({
        where: {
          identifier: email,
        },
      });
      if (!user) {
        console.log(`User not found for email: ${email}`);
        continue;
      }
      const customer = await this.customerService.findOneByUserId(ctx, user.id);
      if (!customer) {
        console.log(`Customer not found for user: ${user.id}`);
        continue;
      }
      await this.customerService.update(ctx, {
        phoneNumber: data.phone,
        id: customer.id,
      });
      this.channelService.assignToChannels(ctx, Customer, customer.id, [3]);
      for (const orderData of data.orders) {
        const existingOrder = await this.orderService.findOneByCode(
          ctx,
          orderData.code,
          ["lines"]
        );
        // if (!order) {
        //   console.log(`Order not found for code: ${orderData.code}`);
        //   continue;
        // }
        if (existingOrder) {
          if (existingOrder.lines.find((l) => l.listPrice === 740000)) {
            console.log(`Delete order ${existingOrder.code}`);
            await this.orderService.deleteOrder(ctx, existingOrder.id);
            console.log(`Deleted order ${existingOrder.code}`);
          } else {
            continue;
          }
        }
        const order = await this.orderService.create(ctx, user.id);
        console.log(`Created order ${orderData.code}`);

        const subTotalPrice = orderData.products.reduce(
          (acc: number, item: any) =>
            acc + item.priceAfterDiscount * item.quantity,
          0
        );
        this.channelService.assignToChannels(ctx, Order, order.id, [3]);

        await this.orderService.setShippingAddress(ctx, order.id, {
          fullName: `${data.firstName} ${data.lastName}`,
          streetLine1: data.address,
          streetLine2: "",
          city: "",
          countryCode: "VN",
          phoneNumber: data.phone,
        });

        for (const item of orderData.products) {
          await this.orderService.addItemToOrder(
            ctx,
            order.id,
            item.productVariantId,
            item.quantity
          );
        }

        await this.connection.getRepository(ctx, Order).update(order.id, {
          createdAt: orderData.createdAt,
          subTotal: subTotalPrice,
          subTotalWithTax: subTotalPrice,
          state: "PaymentSettled",
          code: orderData.code,
          active: false,
        });
        const orderLines = await this.connection
          .getRepository(ctx, OrderLine)
          .find({
            where: {
              order: {
                id: order.id,
              },
            },
          });
        for (const orderLine of orderLines) {
          const productVariant = orderData.products.find(
            (v: any) => v.productVariantId === orderLine.productVariantId
          );
          await this.connection
            .getRepository(ctx, OrderLine)
            .update(orderLine.id, {
              listPrice: productVariant.price,
            });
        }
        await this.orderService.applyCouponCode(
          ctx,
          order.id,
          orderData.discountName
        );
        const payment = await this.connection
          .getRepository(ctx, Payment)
          .findOne({
            where: {
              order: {
                id: order.id,
              },
            },
          });
        if (!payment) {
          await this.paymentService.createManualPayment(
            ctx,
            order,
            subTotalPrice,
            {
              method: "zalopay_vietqr",
              orderId: order.id,
              metadata: {},
            }
          );
        }
      }
    }
  }
  @Get("migrate-customer")
  async migrateCustomer() {
    const ctx = await this.createRequestContext();

    const workbook = XLSX.readFile(
      "/Users/<USER>/Projects/eventista/eventista-vendure/FanpassCustomer.xlsx"
    );
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const sheetData = XLSX.utils.sheet_to_json(sheet);

    for (const row of sheetData) {
      const email = (row as any)["Thông tin khách hàng"];
      const address = (row as any)["Địa chỉ nhận vé"];
      const phone = (row as any)["Số điện thoại"];
      const firstName = (row as any)["First Name"];
      const lastName = (row as any)["Last Name"];

      let user = await this.connection.getRepository(ctx, User).findOne({
        where: {
          identifier: email,
        },
      });
      if (user) {
        console.log(`User already exists for email: ${email}`);
        const customer = await this.customerService.findOneByUserId(
          ctx,
          user.id
        );
        if (!customer) {
          console.log(`Customer not found for user: ${user.id}`);
          continue;
        }
        await this.customerGroupService.addCustomersToGroup(ctx, {
          customerGroupId: 1,
          customerIds: [customer.id],
        });
        continue;
      }
      await this.externalAuthenticationService.createCustomerAndUser(ctx, {
        strategy: "eventista",
        externalIdentifier: "",
        verified: true,
        emailAddress: email,
        firstName,
        lastName,
      });
      user = await this.connection.getRepository(ctx, User).findOne({
        where: {
          identifier: email,
        },
      });
      if (!user) {
        console.log(`User not found for email: ${email}`);
        continue;
      }
      const customer = await this.customerService.findOneByUserId(ctx, user.id);
      if (!customer) {
        console.log(`Customer not found for user: ${user.id}`);
        continue;
      }
      await this.customerService.update(ctx, {
        phoneNumber: phone,
        id: customer.id,
      });
      await this.customerGroupService.addCustomersToGroup(ctx, {
        customerGroupId: 1,
        customerIds: [customer.id],
      });
    }
  }

  private async createRequestContext(): Promise<RequestContext> {
    const channel = await this.channelService.getDefaultChannel();
    return new RequestContext({
      apiType: "admin",
      isAuthorized: true,
      authorizedAsOwnerOnly: false,
      channel,
    });
  }
}
