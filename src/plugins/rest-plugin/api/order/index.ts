import {
  RequestContext,
  ChannelService,
  TransactionalConnection,
  Order,
  PaginatedList,
  OrderState,
} from "@vendure/core";
import {
  Controller,
  Get,
  Post,
  Query,
  Res,
  Header,
  Body,
  Req,
} from "@nestjs/common";
import { Brackets } from "typeorm";
import * as XLSX from "xlsx";
import { Response } from "express";
import { formatInTimeZone } from "date-fns-tz";

interface OrderQueryOptions {
  ctx: RequestContext;
  keyword?: string;
  state?: OrderState;
  sortKey: string;
  sortDirection: string;
  skip?: number;
  take?: number;
  startDate?: string;
  endDate?: string;
  channelId?: string;
  paymentMethod?: string;
  productIds?: string[];
}

@Controller("api/v1/orders")
export class OrderController {
  constructor(
    private connection: TransactionalConnection,
    private channelService: ChannelService
  ) {}

  @Get("")
  async listOrders(
    @Req() req: any,
    @Query("limit") limit: number,
    @Query("page") page: number,
    @Query("keyword") keyword: string,
    @Query("sort_key") sortKey: string,
    @Query("sort_direction") sortDirection: string,
    @Query("state") state: OrderState,
    @Query("start_date") startDate: string,
    @Query("end_date") endDate: string,
    @Query("payment_method") paymentMethod: string,
    @Query("productIds[]") productIds?: string | string[]
  ) {
    // Get context from request
    const ctx = req.vendureContext;
    let productIdsArray: string[] = [];
    if (productIds) {
      productIdsArray = Array.isArray(productIds) ? productIds : [productIds];
    }
    const { orders, totalItems } = await this.findOrders({
      ctx,
      keyword,
      state,
      sortKey,
      sortDirection,
      skip: (page - 1) * limit,
      take: limit,
      startDate,
      endDate,
      paymentMethod,
      productIds: productIdsArray,
    });

    return {
      items: orders,
      totalItems,
    } as PaginatedList<Order>;
  }

  @Post("export")
  @Header(
    "Content-Type",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
  )
  @Header("Content-Disposition", "attachment; filename=orders.xlsx")
  async exportOrders(
    @Req() req: any,
    @Res() res: Response,
    @Body()
    body: {
      limit: number;
      page: number;
      keyword: string;
      sort_key: string;
      sort_direction: string;
      state: OrderState;
      start_date: string;
      end_date: string;
      channelToken: string;
      payment_method: string;
      productIds: string[];
    }
  ) {
    // Get context from request
    const ctx = req.vendureContext;

    const { orders } = await this.findOrders({
      ctx,
      keyword: body.keyword,
      state: body.state,
      sortKey: body.sort_key,
      sortDirection: body.sort_direction,
      startDate: body.start_date,
      endDate: body.end_date,
      paymentMethod: body.payment_method,
      productIds: body.productIds,
      take: 0,
    });
    // console.log(111, JSON.stringify(orders));

    // Transform orders to simplified format for Excel
    const formattedOrders = orders.reduce<any[]>((acc, order) => {
      const discount = order.lines.reduce((total, line) => {
        const discount = line.discounts.reduce((acc, discount) => {
          return acc + discount.amount;
        }, 0);
        return total + discount;
      }, 0);
      order.lines.forEach((line) => {
        acc.push({
          "Mã đơn hàng": order.code,
          "Thời gian giao dịch": formatInTimeZone(
            order.payments?.[0]?.createdAt || order.createdAt,
            "Asia/Ho_Chi_Minh",
            "dd/MM/yyyy HH:mm:ss"
          ),
          "Trạng thái đơn hàng": "Đã thanh toán",
          "Email khách hàng": order.customer?.emailAddress,
          // "Tên khách hàng":
          //   order.customer?.addresses?.[0]?.fullName ||
          //   order.customer?.firstName + " " + order.customer?.lastName,
          // "Số điện thoại khách hàng":
          //   order.customer?.addresses?.[0]?.phoneNumber ||
          //   order.customer?.phoneNumber,
          // "Địa chỉ khách hàng": `${
          //   order.customer?.addresses?.[0]?.streetLine1
          // }, ${(order.customer?.addresses?.[0]?.customFields as any)?.ward}, ${
          //   (order.customer?.addresses?.[0]?.customFields as any)?.district
          // }, ${order.customer?.addresses?.[0]?.province}`,
          "Tên sản phẩm": line.productVariant?.translations[0].name,
          "Số lượng sản phẩm": line.quantity,
          "Tên người nhận": order.shippingAddress?.fullName,
          "Số điện thoại người nhận": order.shippingAddress?.phoneNumber,
          "Địa chỉ giao hàng": `${order.shippingAddress?.streetLine1 || ""}, ${
            order.shippingAddress?.customFields?.ward || ""
          }, ${order.shippingAddress?.customFields?.district || ""}, ${
            order.shippingAddress?.province || ""
          }`,
          "Loại thanh toán": order.payments?.[0]?.method,
          "Ngày tạo": formatInTimeZone(
            order.createdAt,
            "Asia/Ho_Chi_Minh",
            "dd/MM/yyyy HH:mm:ss"
          ),
          "Trạng thái giao dịch": "Giao dịch thành công",
          "Ngày giao dự kiến": "-",
          "Giá trị đơn hàng": order.subTotal - discount,
          "Phí ship": 0,
          "Mã khuyến mãi": order.couponCodes.join(", "),
          "Giá trị khuyến mãi": discount,
          "Thành tiền": order.subTotal,
          "Hoàn tiền": "-",
          "Cổng thanh toán": "ZaloPay",
        });
      });
      return acc;
    }, []);

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(formattedOrders);

    // Column width settings
    const columnWidths = [
      { wch: 15 }, // Mã đơn hàng
      { wch: 15 }, // Thời gian giao dịch
      { wch: 10 }, // Trạng thái
      { wch: 20 }, // Tổng tiền
      { wch: 25 }, // Tên khách hàng
      { wch: 15 }, // Email khách hàng
      { wch: 15 }, // Số điện thoại khách hàng
      { wch: 15 }, // Tên người nhận
      { wch: 15 }, // Số điện thoại người nhận
      { wch: 20 }, // Loại thanh toán
      { wch: 40 }, // Trạng thái thanh toán
      { wch: 50 }, // Số lượng sản phẩm
      { wch: 15 }, // Mã khuyến mãi
      // { wch: 15 }, // Giá trị đơn hàng
      // { wch: 15 }, // Phí ship
      // { wch: 15 }, // Giá trị khuyến mãi
      { wch: 15 }, // Địa chỉ nhận hàng
      { wch: 15 }, // Hoàn tiền
      { wch: 15 }, // Ngày tạo
      { wch: 15 }, // Trạng thái giao hàng
      { wch: 15 }, // Ngày giao dự kiến
      { wch: 15 }, // Tên sản phẩm
      { wch: 15 }, // Cổng thanh toán
      { wch: 15 }, // Thành tiền
    ];
    worksheet["!cols"] = columnWidths;

    // Create workbook
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Orders");

    // Generate buffer
    const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

    // Send the file
    res.send(buffer);
  }

  /**
   * Find orders based on query parameters
   */
  private async findOrders(
    options: OrderQueryOptions
  ): Promise<{ orders: Order[]; totalItems: number }> {
    const {
      ctx,
      keyword,
      state,
      sortKey,
      sortDirection,
      skip,
      take = 10,
      startDate,
      endDate,
      paymentMethod,
      productIds,
    } = options;

    let orders: Order[] = [];
    let totalItems: number = 0;

    // Find all by keyword if provided
    if (keyword) {
      const countQueryBuilder = this.getCommonCountQueryBuilder(ctx);
      const queryBuilder = this.getCommonQueryBuilder(ctx);
      // Try to find by order code first
      queryBuilder.andWhere("order.code = :keyword", { keyword });

      if (state) {
        queryBuilder.andWhere("order.state = :state", { state });
        countQueryBuilder.andWhere("order.state = :state", { state });
      }

      if (paymentMethod) {
        queryBuilder.andWhere("payments.method = :paymentMethod", {
          paymentMethod,
        });
        countQueryBuilder.andWhere("payments.method = :paymentMethod", {
          paymentMethod,
        });
      }

      if (startDate && endDate) {
        queryBuilder.andWhere(
          "order.createdAt BETWEEN :startDate AND :endDate",
          {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
          }
        );
        countQueryBuilder.andWhere(
          "order.createdAt BETWEEN :startDate AND :endDate",
          {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
          }
        );
      }
      if (productIds && productIds.length > 0) {
        queryBuilder.andWhere("productVariant.productId IN (:...productIds)", {
          productIds,
        });
        countQueryBuilder.andWhere(
          "productVariant.productId IN (:...productIds)",
          {
            productIds,
          }
        );
      }
      queryBuilder.orderBy("order.createdAt", "DESC");
      if (skip) {
        queryBuilder.offset(skip);
      }
      if (take) {
        queryBuilder.limit(take);
      }

      if (sortKey) {
        queryBuilder.orderBy(
          `order.${sortKey}`,
          sortDirection === "asc" ? "ASC" : "DESC"
        );
      }

      // console.log(11111, queryBuilder.getQueryAndParameters());

      const codeOrders = await queryBuilder.getMany();
      const countResult = await countQueryBuilder.getRawOne();
      const total = parseInt(countResult.lineCount, 10);

      if (codeOrders.length > 0) {
        orders = codeOrders;
        totalItems = total;
      } else {
        // Try to find by customer details
        const queryBuilder = this.getCommonQueryBuilder(ctx);
        const countQueryBuilder = this.getCommonCountQueryBuilder(ctx);
        if (state) {
          queryBuilder.andWhere("order.state = :state", { state });
          countQueryBuilder.andWhere("order.state = :state", { state });
        }
        if (paymentMethod) {
          queryBuilder.andWhere("order.payments.method = :paymentMethod", {
            paymentMethod,
          });
          countQueryBuilder.andWhere("payments.method = :paymentMethod", {
            paymentMethod,
          });
        }
        if (startDate && endDate) {
          queryBuilder.andWhere(
            "order.createdAt BETWEEN :startDate AND :endDate",
            {
              startDate: new Date(startDate),
              endDate: new Date(endDate),
            }
          );
          countQueryBuilder.andWhere(
            "order.createdAt BETWEEN :startDate AND :endDate",
            {
              startDate: new Date(startDate),
              endDate: new Date(endDate),
            }
          );
        }
        if (productIds && productIds.length > 0) {
          queryBuilder.andWhere(
            "productVariant.productId IN (:...productIds)",
            {
              productIds,
            }
          );
          countQueryBuilder.andWhere(
            "productVariant.productId IN (:...productIds)",
            {
              productIds,
            }
          );
        }
        queryBuilder.andWhere(
          new Brackets((qb) => {
            qb.orWhere("customer.emailAddress = :keyword", { keyword });
            qb.orWhere("customer.phoneNumber = :keyword", {
              keyword,
            });
          })
        );
        countQueryBuilder.andWhere(
          new Brackets((qb) => {
            qb.orWhere("customer.emailAddress = :keyword", { keyword });
            qb.orWhere("customer.phoneNumber = :keyword", {
              keyword,
            });
          })
        );
        queryBuilder.orderBy("order.createdAt", "DESC");

        if (sortKey) {
          queryBuilder.orderBy(
            `order.${sortKey}`,
            sortDirection === "asc" ? "ASC" : "DESC"
          );
        }
        if (skip) {
          queryBuilder.offset(skip);
        }
        if (take) {
          queryBuilder.limit(take);
        }

        orders = await queryBuilder.getMany();

        const customerCountResult = await countQueryBuilder.getRawOne();
        const customerTotal = parseInt(customerCountResult.lineCount, 10);

        totalItems = customerTotal;
      }
    } else {
      // No keyword, find all orders with optional state filter
      // Get total count of order lines for these conditions
      const countQueryBuilder = this.getCommonCountQueryBuilder(ctx);
      const queryBuilder = this.getCommonQueryBuilder(ctx);

      if (state) {
        queryBuilder.andWhere("order.state = :state", { state });
        countQueryBuilder.andWhere("order.state = :state", { state });
      }
      if (startDate && endDate) {
        queryBuilder.andWhere(
          "order.createdAt BETWEEN :startDate AND :endDate",
          {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
          }
        );
        countQueryBuilder.andWhere(
          "order.createdAt BETWEEN :startDate AND :endDate",
          {
            startDate: new Date(startDate),
            endDate: new Date(endDate),
          }
        );
      }
      if (paymentMethod) {
        queryBuilder.andWhere("payments.method = :paymentMethod", {
          paymentMethod,
        });
        countQueryBuilder.andWhere("payments.method = :paymentMethod", {
          paymentMethod,
        });
      }

      if (productIds && productIds.length > 0) {
        queryBuilder.andWhere("productVariant.productId IN (:...productIds)", {
          productIds,
        });
        countQueryBuilder.andWhere(
          "productVariant.productId IN (:...productIds)",
          {
            productIds,
          }
        );
      }
      queryBuilder.orderBy("order.createdAt", "DESC");
      // Get orders with their relations
      if (skip) {
        queryBuilder.offset(skip);
      }
      if (take) {
        queryBuilder.limit(take);
      }
      // console.log(queryBuilder.getQueryAndParameters());
      // console.log(1111, skip, take, typeof skip, typeof take);
      orders = await queryBuilder.getMany();

      const counts = await countQueryBuilder.getRawOne();

      totalItems = parseInt(counts.lineCount, 10);
    }

    return { orders, totalItems };
  }

  private getCommonQueryBuilder(ctx: RequestContext) {
    const qb = this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder("order")
      .leftJoinAndSelect("order.customer", "customer")
      .leftJoinAndSelect("customer.addresses", "customerAddresses")
      .leftJoinAndSelect("order.lines", "lines")
      .leftJoinAndSelect("order.channels", "orderChannelsChannel")
      .leftJoinAndSelect("lines.productVariant", "productVariant")
      .leftJoinAndSelect(
        "productVariant.translations",
        "productVariantTranslations"
      )
      .leftJoinAndSelect("order.payments", "payments");

    if (ctx.channel.id) {
      qb.andWhere("orderChannelsChannel.id = :channelId", {
        channelId: ctx.channel.id,
      });
    }
    return qb;
  }

  private getCommonCountQueryBuilder(ctx: RequestContext) {
    const qb = this.connection
      .getRepository(ctx, Order)
      .createQueryBuilder("order")
      .select("COUNT(lines.id)", "lineCount")
      .leftJoin("order.customer", "customer")
      .leftJoin("customer.addresses", "customerAddresses")
      .leftJoin("order.lines", "lines")
      .leftJoin("lines.productVariant", "productVariant")
      .leftJoin("order.payments", "payments")
      .leftJoin("order.channels", "orderChannelsChannel");

    if (ctx.channel.id) {
      qb.andWhere("orderChannelsChannel.id = :channelId", {
        channelId: ctx.channel.id,
      });
    }
    return qb;
  }
}
