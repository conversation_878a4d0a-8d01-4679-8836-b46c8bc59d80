import {
  FulfillmentHandler,
  Injector,
  LanguageCode,
  Order,
  OrderService,
  StockLocationService,
  TransactionalConnection,
} from "@vendure/core";
import { ViettelProvider } from "../../plugins/shipping/providers/viettel";
import { getSecretKey } from "../../utils/getSecretKey";

let orderService: OrderService;
let stockLocationService: StockLocationService;
let connection: TransactionalConnection;
let viettelProvider: ViettelProvider;

export const viettelPostFulfillmentHandler = new FulfillmentHandler({
  args: {},
  code: "viettel-post",
  description: [
    {
      value: "Generate tracking shipping code via viettel-post",
      languageCode: LanguageCode.vi,
    },
  ],
  init: async (inj: Injector) => {
    orderService = inj.get(OrderService);
    stockLocationService = inj.get(StockLocationService);
    connection = inj.get(TransactionalConnection);
    viettelProvider = new ViettelProvider(
      inj.get(TransactionalConnection),
      await getSecret<PERSON>ey()
    );
  },
  createFulfillment: async (ctx, orders) => {
    if (orders.length > 1) {
      throw new Error("Can not create multiple order");
    }
    if (orders.length > 0) {
      const order = await orderService.findOne(ctx, orders[0].id, [
        'payments',
        'lines',
        'lines.productVariant'
      ]);
      if (!order) {
        throw new Error("Order not found");
      }
      const stocks = await stockLocationService.findAll(ctx);
      if (!stocks.items.length) {
        throw new Error("Stock not found");
      }
      try {
        const res = await viettelProvider.createOrder(ctx, {
          order,
          stock: stocks.items[0],
        });
        await connection.getRepository(ctx, Order).update(order.id, {
          shipping: res.MONEY_TOTAL,
        });

        return {
          trackingCode: res.ORDER_NUMBER,
          customFields: res,
        };
      } catch (error) {
        throw new Error(`Failed to create order: ${error}`);
      }
    }
    return {};
  },
});
