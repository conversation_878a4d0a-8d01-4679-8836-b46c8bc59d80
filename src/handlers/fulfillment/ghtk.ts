// import { Fulfillment<PERSON><PERSON><PERSON>, Injector, LanguageCode, OrderService, StockLocationService } from "@vendure/core";
// import {createOrder} from '../../utils/ghtk'

// let orderService: OrderService
// let stockLocationService: StockLocationService

// export const ghtkFulfillmentHandler = new FulfillmentHandler({
//     args: {},
//     code: 'ghtk',
//     description: [{
//         value: 'Generate tracking shipping code via ghtk',
//         languageCode: LanguageCode.vi
//     }],
//     init: (inj: Injector) => {
//         orderService = inj.get(OrderService)
//         stockLocationService = inj.get(StockLocationService)
//     },
//     createFulfillment: async(ctx, orders) => {
//         if (orders.length > 1) {
//             throw new Error('Can not create multiple order')
//         }
//         if (orders.length > 0) {
//             const order = await orderService.findOne(ctx, orders[0].id)
//             if (!order) {
//                 return {}
//             }
//             const stocks = await stockLocationService.findAll(ctx)
//             if (!stocks.items.length) {
//                 return {}
//             }
//             const res = await createOrder(order, stocks.items[0])
//             return {
//                 trackingCode: res.label,
//                 customFields: {
//                     trackingId: res.tracking_id,
//                     estimated_pick_time: res.estimated_pick_time,
//                     estimated_deliver_time: res.estimated_deliver_time,
//                     status_id: res.status_id
//                 }
//             }
//         }
//         return {}
//     }
// })
