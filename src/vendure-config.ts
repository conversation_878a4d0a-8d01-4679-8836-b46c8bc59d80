import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ultMoneyStrategy,
  DefaultSearchPlugin,
  LogLevel,
  NativeAuthenticationStrategy,
  VendureConfig,
  defaultOrderProcess,
} from "@vendure/core";
import { EmailPlugin, FileBasedTemplateLoader } from "@vendure/email-plugin";
import { AssetServerPlugin } from "@vendure/asset-server-plugin";
import { AdminUiPlugin } from "@vendure/admin-ui-plugin";
import "dotenv/config";
import path from "path";
import { ShippingPlugin } from "./plugins/shipping";
import { OrderPlugin } from "./plugins/order/shop-api";
import { ProductPlugin } from "./plugins/product";
import { zaloPaymentHandler } from "./plugins/payment-plugin/zalo";
import { RestPlugin } from "./plugins/rest-plugin";
import { GoogleAuthenticationStrategy } from "./plugins/authentication/google-authentication-strategy";
import { EventistaAuthenticationStrategy } from "./plugins/authentication/eventista-authentication-strategy";
import { CustomEmailPlugin } from "./plugins/email";
import { TelemetryPlugin } from "@vendure/telemetry-plugin";
import { viettelPostFulfillmentHandler } from "./handlers/fulfillment/viettel-post";
import { codOrderProcess } from "./plugins/order/cod-order-process";
import { DstAuthenticationStrategy } from "./plugins/authentication/dst-authentication-strategy";
import { CoreCachePlugin } from "./plugins/core-cache/core-cache.plugin";

const IS_DEV = process.env.APP_ENV === "dev";
const serverPort = +process.env.PORT || 5678;

export class NoneDecimalPlacesMoneyStrategy extends DefaultMoneyStrategy {
  readonly precision = 0;
}

export const config: VendureConfig = {
  apiOptions: {
    hostname: "0.0.0.0",
    port: serverPort,
    adminApiPath: "admin-api",
    shopApiPath: "v1/vendure/shop-api",
    // The following options are useful in development mode,
    // but are best turned off for production for security
    // reasons.
    ...(IS_DEV
      ? {
          adminApiPlayground: {
            settings: { "request.credentials": "include" },
          },
          adminApiDebug: true,
          shopApiPlayground: {
            settings: { "request.credentials": "include" },
          },
          shopApiDebug: true,
        }
      : {}),
  },
  authOptions: {
    tokenMethod: ["bearer", "cookie"],
    superadminCredentials: {
      identifier: process.env.SUPERADMIN_USERNAME,
      password: process.env.SUPERADMIN_PASSWORD,
    },
    cookieOptions: {
      secret: process.env.COOKIE_SECRET,
    },
    shopAuthenticationStrategy: [
      new NativeAuthenticationStrategy(),
      new EventistaAuthenticationStrategy(),
      new GoogleAuthenticationStrategy(process.env.GOOGLE_CLIENT_ID),
      new DstAuthenticationStrategy(),
    ],
    requireVerification: false,
  },
  entityOptions: {
    moneyStrategy: new NoneDecimalPlacesMoneyStrategy(),
  },
  dbConnectionOptions: {
    type: "postgres",
    // See the README.md "Migrations" section for an explanation of
    // the `synchronize` and `migrations` options.
    synchronize: false,
    migrations: [path.join(__dirname, "./migrations/*.+(js|ts)")],
    logging: false,
    database: process.env.DB_NAME,
    schema: process.env.DB_SCHEMA,
    host: process.env.DB_HOST,
    port: +process.env.DB_PORT,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
  },
  paymentOptions: {
    paymentMethodHandlers: [zaloPaymentHandler],
  },
  // When adding or altering custom field definitions, the database will
  // need to be updated. See the "Migrations" section in README.md.
  customFields: {
    Customer: [
      {
        name: "externalUserId",
        type: "string",
        nullable: true,
      },
    ],
    Address: [
      { name: "district", type: "string", nullable: true },
      { name: "ward", type: "string", nullable: true },
    ],
    ProductVariant: [
      { name: "compareAtPrice", type: "float", nullable: true },
      { name: "limited", type: "boolean", nullable: true },
    ],
    Product: [
      {
        name: "references",
        type: "struct",
        fields: [{ name: "url", type: "string" }],
        nullable: true,
        list: true,
      },
      {
        name: "function",
        type: "string",
        nullable: true,
      },
      {
        name: "ingredient",
        type: "string",
        nullable: true,
      },
      {
        name: "brand",
        type: "string",
        nullable: true,
      },
      {
        name: "manufacturer",
        type: "string",
        nullable: true,
      },
    ],
    StockLocation: [
      {
        name: "phone",
        type: "string",
        nullable: true,
      },
      {
        name: "address",
        type: "string",
        nullable: true,
      },
      {
        name: "province",
        type: "string",
        nullable: true,
      },
      {
        name: "district",
        type: "string",
        nullable: true,
      },
      {
        name: "ward",
        type: "string",
        nullable: true,
      },
      {
        name: "hamlet",
        type: "string",
        nullable: true,
      },
    ],
    ShippingMethod: [{ name: "token", type: "text", nullable: true }],
    Fulfillment: [{ name: "extraData", type: "text", nullable: true }],
  },
  plugins: [
    // Core Cache Plugin - MUST be first to intercept all API calls
    CoreCachePlugin.init({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        cluster: process.env.REDIS_CLUSTER === 'true',
      },
      defaultTTL: 300, // 5 minutes default
      enableMetrics: true,
    }),
    ShippingPlugin,
    OrderPlugin,
    CustomEmailPlugin,
    AssetServerPlugin.init({
      route: "assets",
      assetUploadDir: path.join(__dirname, "../static/assets"),
      // For local dev, the correct value for assetUrlPrefix should
      // be guessed correctly, but for production it will usually need
      // to be set manually to match your production url.
      assetUrlPrefix: IS_DEV
        ? undefined
        : "https://merchandise.faniesta.com/assets/",
    }),
    DefaultJobQueuePlugin.init({ useDatabaseForBuffer: true }),
    DefaultSearchPlugin.init({ bufferUpdates: false, indexStockStatus: true }),
    EmailPlugin.init({
      outputPath: path.join(__dirname, "../static/email/test-emails"),
      route: "mailbox",
      handlers: [],
      templateLoader: new FileBasedTemplateLoader(
        path.join(__dirname, "../static/email/templates")
      ),
      // transport: {
      //   type: "ses",
      //   SES: { ses },
      // },
      transport: {
        type: "smtp",
        host: "pro12.emailserver.vn",
        port: 465,
        secure: true,
        auth: {
          user: "<EMAIL>",
          pass: "Eventista@2024",
        },
        logging: true,
        debug: true,
      },
      globalTemplateVars: {
        // The following variables will change depending on your storefront implementation.
        // Here we are assuming a storefront running at http://localhost:8080.
        fromAddress: '"Eventista" <<EMAIL>>',
        verifyEmailAddressUrl: "http://localhost:8080/verify",
        passwordResetUrl: "http://localhost:8080/password-reset",
        changeEmailAddressUrl:
          "http://localhost:8080/verify-email-address-change",
      },
    }),
    AdminUiPlugin.init({
      route: "admin",
      port: serverPort + 2,
      adminUiConfig: {
        // apiPort: 5678,
        apiPort: IS_DEV ? serverPort : 443,
      },
    }),
    RestPlugin,
    TelemetryPlugin.init({
      loggerOptions: {
        logToConsole: IS_DEV ? LogLevel.Verbose : LogLevel.Warn,
      },
    }),
    ProductPlugin,
  ],
  shippingOptions: {
    fulfillmentHandlers: [viettelPostFulfillmentHandler],
  },
  logger: new DefaultLogger({ level: IS_DEV ? LogLevel.Debug : LogLevel.Info }),
  orderOptions: {
    process: [defaultOrderProcess, codOrderProcess],
  },
};
