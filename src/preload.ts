import { OTLPLogExporter } from "@opentelemetry/exporter-logs-otlp-proto";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { BatchLogRecordProcessor } from "@opentelemetry/sdk-logs";
import { NodeSDK } from "@opentelemetry/sdk-node";
import { BatchSpanProcessor } from "@opentelemetry/sdk-trace-base";
import { getSdkConfiguration } from "@vendure/telemetry-plugin/preload";
import "dotenv/config";

const IS_DEV = process.env.APP_ENV === "dev";

// Configure exporters with timeouts to prevent memory leaks
const traceExporter = new OTLPTraceExporter({
  // Add timeout to prevent hanging requests
  timeoutMillis: 5000,
});

const logExporter = new OTLPLogExporter({
  // Add timeout to prevent hanging requests
  timeoutMillis: 5000,
});

// Configure batch processors with memory limits
const spanProcessor = new BatchSpanProcessor(traceExporter, {
  maxQueueSize: 1000, // Limit queue size to prevent memory buildup
  maxExportBatchSize: 512, // Smaller batch size for frequent exports
  exportTimeoutMillis: 2000, // Timeout for export operations
  scheduledDelayMillis: 1000, // More frequent exports (default: 5000ms)
});

const logProcessor = new BatchLogRecordProcessor(logExporter, {
  maxQueueSize: 1000, // Limit queue size to prevent memory buildup
  maxExportBatchSize: 512, // Smaller batch size for frequent exports
  exportTimeoutMillis: 2000, // Timeout for export operations
  scheduledDelayMillis: 1000, // More frequent exports (default: 5000ms)
});

const config = getSdkConfiguration({
  config: {
    spanProcessors: [spanProcessor],
    logRecordProcessors: [logProcessor],
  },
});

const sdk = new NodeSDK(config);

// Optional: Memory monitoring in production
if (!IS_DEV) {
  setInterval(() => {
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024),
    };

    // Log memory usage if it's getting high
    if (memUsageMB.heapUsed > 1000) {
      // > 1GB
      console.warn(`High memory usage detected:`, memUsageMB);
    }
  }, 60000); // Check every minute
}

// Add graceful shutdown handling
process.on("SIGTERM", async () => {
  try {
    await sdk.shutdown();
    console.log("Telemetry terminated");
  } catch (error) {
    console.error("Error terminating telemetry", error);
  }
});

process.on("SIGINT", async () => {
  try {
    await sdk.shutdown();
    console.log("Telemetry terminated");
  } catch (error) {
    console.error("Error terminating telemetry", error);
  }
});

sdk.start();
