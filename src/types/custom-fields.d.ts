// This file defines the TypeScript types for custom fields configured in vendure-config.ts
// It uses module augmentation to extend Vendure's built-in custom field types

declare module "@vendure/core/dist/entity/custom-entity-fields" {
  interface CustomCustomerFields {
    externalUserId?: string;
  }

  interface CustomAddressFields {
    district?: string;
    ward?: string;
  }

  interface CustomProductVariantFields {
    compareAtPrice?: number;
    limited?: boolean;
  }

  interface CustomProductFields {
    references?: Array<{ url: string }>;
    function?: string;
    ingredient?: string;
    brand?: string;
    manufacturer?: string;
  }

  interface CustomStockLocationFields {
    phone?: string;
    address?: string;
    province?: string;
    district?: string;
    ward?: string;
    hamlet?: string;
  }

  interface CustomShippingMethodFields {
    token?: string;
  }
}

// Re-export for convenience
export {};
