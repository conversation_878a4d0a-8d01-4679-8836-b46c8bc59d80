{"name": "commerce", "version": "0.1.0", "private": true, "scripts": {"dev:server": "ts-node ./src/index.ts", "dev:worker": "ts-node ./src/index-worker.ts", "dev": "concurrently npm:dev:*", "copy-env-dev": "cp .env.dev .env", "copy-env-prod": "cp .env.prod .env", "build-dev": "npm run copy-env-dev && tsc", "build-prod": "npm run copy-env-prod && tsc", "start:server": "NEW_RELIC_APP_NAME=merchandise NEW_RELIC_LICENSE_KEY=2c477b014a68f4ca90e7f4d576941d04FFFFNRAL ts-node -r newrelic ./dist/index.js", "start:worker": "NEW_RELIC_APP_NAME=merchandise NEW_RELIC_LICENSE_KEY=2c477b014a68f4ca90e7f4d576941d04FFFFNRAL ts-node -r newrelic ./dist/index-worker.js", "start": "concurrently npm:start:*", "codegen": "graphql-codegen --config codegen.ts"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.840.0", "@aws-sdk/client-ses": "^3.723.0", "@types/crypto-js": "^4.2.2", "@vendure/admin-ui-plugin": "3.3.5", "@vendure/asset-server-plugin": "3.3.5", "@vendure/core": "3.3.5", "@vendure/email-plugin": "3.3.5", "@vendure/telemetry-plugin": "3.3.5", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "16.4.5", "google-auth-library": "^9.15.0", "newrelic": "^12.24.0", "pg": "8.12.0", "typeorm": "^0.3.22", "xlsx": "^0.18.5"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.3", "@graphql-codegen/typescript": "^4.1.2", "@types/mjml": "^4.7.4", "@vendure/cli": "3.3.5", "concurrently": "9.0.1", "typescript": "5.3.3"}}